package cn.gov.zcy.bigdata.datapi.sdk;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 签名工具类，用于生成基于HMAC-SHA256的安全签名
 */
final class SignUtil {
    private static final Logger log = LoggerFactory.getLogger(SignUtil.class);
    private static final String ALGORITHM = "HmacSHA256";
    private static final String ENCODING = StandardCharsets.UTF_8.name();

    private SignUtil() {
        // 私有构造方法防止实例化
    }

    /**
     * 使用HMAC-SHA256算法生成签名
     *
     * @param plainText 需要签名的文本
     * @param secret 密钥
     * @return 生成的Base64 URL安全编码的签名字符串
     */
    static String generateSign(String plainText, String secret) {
        try {
            Mac mac = Mac.getInstance(ALGORITHM);
            byte[] secretBytes = secret.getBytes(StandardCharsets.UTF_8);
            mac.init(new SecretKeySpec(secretBytes, ALGORITHM));
            
            byte[] signatureBytes = mac.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64URLSafeString(signatureBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("算法不存在: {}", ALGORITHM, e);
            throw new RuntimeException("签名算法不存在: " + ALGORITHM, e);
        } catch (InvalidKeyException e) {
            log.error("无效的密钥: {}", secret, e);
            throw new RuntimeException("无效的签名密钥", e);
        }
    }
}
