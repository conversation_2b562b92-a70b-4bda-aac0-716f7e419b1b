package cn.gov.zcy.bigdata.datapi.sdk;

import com.alibaba.fastjson2.JSON;
import java.lang.reflect.Type;

class JsonTool {
    JsonTool() {
    }

    public static <T> T deserialization(String json, Type type) {
        return String.class.equals(type) ? (T) json : JSON.parseObject(json, type);
    }

    public static byte[] serialization(Object obj) {
        return JSON.toJSONBytes(obj);
    }
}
