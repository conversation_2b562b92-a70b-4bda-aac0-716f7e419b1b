package cn.gov.zcy.bigdata.datapi.sdk;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import okhttp3.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class DatapiClient {
    private static final Logger log = LoggerFactory.getLogger(DatapiClient.class);
    private static final String X_TIMESTAMP = "x-timestamp";
    private static final String X_SIGNATURE = "x-signature";
    private static final String X_APPKEY = "x-appKey";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final List<String> SIGN_HEADERS = List.of(X_TIMESTAMP, X_APPKEY);

    private static OkHttpClient HTTP_CLIENT;
    private static String appKey;
    private static String appSecret;
    private static String gatewayEntryUrl;
    private static volatile boolean isInited;

    private DatapiClient() {
        // 私有构造函数防止实例化
    }

    /**
     * 初始化数据API客户端（使用自定义HTTP客户端）
     *
     * @param appKey          应用密钥
     * @param appSecret       应用秘钥
     * @param gatewayEntryUrl 网关入口URL
     * @param httpClient      自定义HTTP客户端
     */
    public static void init(String appKey, String appSecret, String gatewayEntryUrl, OkHttpClient httpClient) {
        initClient(appKey, appSecret, gatewayEntryUrl, httpClient);
    }

    /**
     * 初始化数据API客户端（使用默认HTTP客户端）
     *
     * @param appKey          应用密钥
     * @param appSecret       应用秘钥
     * @param gatewayEntryUrl 网关入口URL
     */
    public static void init(String appKey, String appSecret, String gatewayEntryUrl) {
        initClient(appKey, appSecret, gatewayEntryUrl, null);
    }

    private static synchronized void initClient(String appKey, String appSecret, String gatewayEntryUrl, OkHttpClient httpClient) {
        isInited = false;
        DatapiClient.appKey = appKey;
        DatapiClient.appSecret = appSecret;

        if (gatewayEntryUrl != null && !gatewayEntryUrl.trim().isEmpty()) {
            DatapiClient.gatewayEntryUrl = gatewayEntryUrl;

            if (httpClient == null) {
                // 创建一个配置更加合理的默认HTTP客户端
                HTTP_CLIENT = new OkHttpClient.Builder()
                        .connectTimeout(10, TimeUnit.SECONDS)
                        .readTimeout(30, TimeUnit.SECONDS)
                        .writeTimeout(30, TimeUnit.SECONDS)
                        .build();
            } else {
                HTTP_CLIENT = httpClient;
            }

            isInited = true;
        } else {
            throw new IllegalArgumentException("无效的网关入口URL: " + gatewayEntryUrl);
        }
    }

    /**
     * 查询返回JSONObject列表的API
     *
     * @param openApiRequest API请求
     * @return 响应的JSON对象列表
     */
    public static List<JSONObject> query(DatapiRequest openApiRequest) {
        try {
            return invoke(openApiRequest, new TypeReference<>() {
            });
        } catch (Exception e) {
            return Collections.emptyList();
        }

    }

    /**
     * 调用API并将结果转换为指定类型
     *
     * @param openApiRequest API请求
     * @param typeReference  返回类型引用
     * @param <T>            返回类型
     * @return 转换后的结果
     */
    public static <T> T invoke(DatapiRequest openApiRequest, TypeReference<T> typeReference) {
        checkInitialized();
        Request request = assemblyRequest(openApiRequest);

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            ResponseBody body = response.body();

            if (response.code() != 200 || body == null) {
                log.error("SDK请求失败, API:{}, HTTP状态码:{}, 响应体:{}",
                        openApiRequest.getApiKey(),
                        response.code(),
                        body == null ? "null" : body.string());
                throw new DatapiResponseException(ErrorCode.SYSTEM_ERROR);
            }

            String bodyStr = body.string();
            try {
                Map<String, Object> responseMap = JsonTool.deserialization(bodyStr, Map.class);
                if (responseMap.get("success") == null || !(Boolean) responseMap.get("success")) {
                    throw new DatapiResponseException(ErrorCode.SYSTEM_ERROR.getCode(), bodyStr);
                }

                Object data = responseMap.get("data");
                if (data == null) {
                    throw new DatapiResponseException(ErrorCode.SYSTEM_ERROR.getCode(), "响应数据为空");
                }

                return JsonTool.deserialization(String.valueOf(data), typeReference.getType());
            } catch (Exception e) {
                log.error("解析响应失败: {}", bodyStr, e);
                throw new DatapiResponseException(ErrorCode.SYSTEM_ERROR.getCode(), e.getMessage());
            }
        } catch (IOException e) {
            log.error("执行请求失败: {}", request, e);
            throw new DatapiResponseException(ErrorCode.SYSTEM_ERROR, e);
        } catch (ClassCastException e) {
            log.error("类型转换失败", e);
            throw new DatapiResponseException(ErrorCode.ERROR_RET_TYPE, e);
        }
    }

    /**
     * 检查客户端是否已初始化
     */
    private static void checkInitialized() {
        if (!isInited) {
            throw new DatapiResponseException(ErrorCode.CLIENT_INIT_ERROR);
        }
    }

    /**
     * 组装HTTP请求
     *
     * @param openApiRequest API请求
     * @return 组装好的HTTP请求
     */
    private static Request assemblyRequest(DatapiRequest openApiRequest) {
        byte[] bodyBytes = JsonTool.serialization(openApiRequest.getParams());
        Map<String, String> headerMap = new HashMap<>(3);
        headerMap.put(X_APPKEY, appKey);
        headerMap.put(X_TIMESTAMP, String.valueOf(System.currentTimeMillis()));

        String signature = generateSignature(openApiRequest.getApiKey(), headerMap, bodyBytes);
        headerMap.put(X_SIGNATURE, signature);

        String url = gatewayEntryUrl + "/" + openApiRequest.getApiKey();
        return new Request.Builder()
                .url(url)
                .headers(Headers.of(headerMap))
                .post(RequestBody.create(JSON_MEDIA_TYPE, bodyBytes))
                .build();
    }

    /**
     * 生成签名
     *
     * @param apiKey    API密钥
     * @param headerMap 请求头映射
     * @param bodyBytes 请求体字节数组
     * @return 签名
     */
    private static String generateSignature(String apiKey, Map<String, String> headerMap, byte[] bodyBytes) {
        String headerString = headerMap.entrySet().stream()
                .filter(entry -> SIGN_HEADERS.contains(entry.getKey()))
                .sorted(Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        String bodyMd5 = Base64.encodeBase64String(DigestUtils.md5(bodyBytes));
        String signPlainText = apiKey + "|" + headerString + "|" + bodyMd5;

        return SignUtil.generateSign(signPlainText, appSecret);
    }

    // 移除主方法，应该在单独的测试类中进行测试
}
