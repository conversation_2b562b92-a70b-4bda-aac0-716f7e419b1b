package com.zjhh.comm.config;

import cn.gov.zcy.bigdata.datapi.sdk.DatapiClient;
import okhttp3.OkHttpClient;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022/5/9 17:58
 */
@Component
public class DatapiCommandLineRunner implements CommandLineRunner {

    @Resource
    private DatapiConfig datapiConfig;


    @Override
    public void run(String... args) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .connectTimeout(300, TimeUnit.SECONDS)
                .writeTimeout(300, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .build();
        DatapiClient.init(datapiConfig.getAppKey(), datapiConfig.getAppSecret(), datapiConfig.getBaseUrl(), client);
    }
}
