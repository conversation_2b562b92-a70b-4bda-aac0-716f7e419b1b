package com.zjhh.comm.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zjhh.comm.serialize.DocIdSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2024/9/30 下午4:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -8320294875537231721L;

    @Schema(description = "id")
    @JsonSerialize(using = DocIdSerializer.class)
    private String id;

    @Schema(description = "文件标题")
    private String title;

    @Schema(description = "文件路径")
    private String path;

    @Schema(description = "文件大小")
    private Long size;

}
