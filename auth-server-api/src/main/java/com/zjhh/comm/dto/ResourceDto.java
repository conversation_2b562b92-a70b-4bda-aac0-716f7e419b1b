package com.zjhh.comm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/11/15 09:44
 */
@Data
public class ResourceDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 7749658141782906739L;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;
}
