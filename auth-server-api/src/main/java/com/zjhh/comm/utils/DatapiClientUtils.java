package com.zjhh.comm.utils;

import cn.gov.zcy.bigdata.datapi.sdk.DatapiClient;
import cn.gov.zcy.bigdata.datapi.sdk.DatapiRequest;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import com.alibaba.fastjson2.*;
import com.zjhh.comm.request.BaseReq;
import com.zjhh.db.comm.Page;
import com.zjhh.db.comm.PageReq;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/17 09:50
 */
public class DatapiClientUtils {

    static List<Integer> startList = new ArrayList<>();
    static List<Integer> endList = new ArrayList<>();

    // 静态初始化块，全局禁用 Jackson 注解支持
    static {
        JSONFactory.setUseJacksonAnnotation(false);
        // JSON.config(JSONWriter.Feature.LargeObject, true);
    }

    //写操作，增、改、查试用。暂不使用
    public static void update(String apiId, BaseReq req) {
        DatapiRequest request = new DatapiRequest(apiId);
        Map<String, Object> params = BeanUtil.beanToMap(req);
        params.forEach(request::putParam);
//        try {
//
//        }
        DatapiClient.query(request);
    }

    public static <T> T queryOne(String apiId, Class<T> tClass) {
        return getOne(DatapiClient.query(new DatapiRequest(apiId)), tClass);
    }

    public static <T> T queryOne(String apiId, String param, String value, Class<T> tClass) {
        return getOne(DatapiClient.query(new DatapiRequest(apiId).putParam(param, value)), tClass);
    }

    public static <T> T queryOne(String apiId, Map<String, Object> params, Class<T> tClass) {
        DatapiRequest request = new DatapiRequest(apiId);
        params.forEach(request::putParam);
        return getOne(DatapiClient.query(request), tClass);
    }

    public static <T> T queryOne(String apiId, BaseReq req, Class<T> tClass) {
        Map<String, Object> params = BeanUtil.beanToMap(req);
        return queryOne(apiId, params, tClass);
    }

    public static <T> List<T> queryList(String apiId, Class<T> tClass) {
        return getList(DatapiClient.query(new DatapiRequest(apiId)), tClass);
    }

    public static <T> List<T> queryList(String apiId, String param, String value, Class<T> tClass) {
        return getList(DatapiClient.query(new DatapiRequest(apiId).putParam(param, value)), tClass);
    }

    public static <T> List<T> queryList(String apiId, Map<String, Object> params, Class<T> tClass) {
        DatapiRequest request = new DatapiRequest(apiId);
        params.forEach(request::putParam);
        return getList(DatapiClient.query(request), tClass);
    }

    public static <T> List<T> queryList(String apiId, BaseReq req, Class<T> tClass) {
        Map<String, Object> params = BeanUtil.beanToMap(req);
        return queryList(apiId, params, tClass);
    }

    public static List<String> queryStringList(String apiId, String param, String value) {
        DatapiRequest request = new DatapiRequest(apiId);
        request.putParam(param, value);
        List<JSONObject> list = DatapiClient.query(request);
        return list.stream().map(jsonObject -> String.valueOf(jsonObject.values().iterator().next())).collect(Collectors.toList());
    }

    public static List<String> queryStringList(String apiId, BaseReq req) {
        DatapiRequest request = new DatapiRequest(apiId);
        Map<String, Object> params = BeanUtil.beanToMap(req);
        params.forEach(request::putParam);
        List<JSONObject> list = DatapiClient.query(request);
        return list.stream().map(jsonObject -> String.valueOf(jsonObject.values().iterator().next())).collect(Collectors.toList());
    }

    public static List<String> queryStringList(String apiId, Map<String, Object> params) {
        DatapiRequest request = new DatapiRequest(apiId);
        params.forEach(request::putParam);
        List<JSONObject> list = DatapiClient.query(request);
        return list.stream().map(jsonObject -> String.valueOf(jsonObject.values().iterator().next())).collect(Collectors.toList());
    }

    public static List<String> queryStringList(String apiId) {
        DatapiRequest request = new DatapiRequest(apiId);
        List<JSONObject> list = DatapiClient.query(request);
        return list.stream().map(jsonObject -> String.valueOf(jsonObject.values().iterator().next())).collect(Collectors.toList());
    }

    public static String queryStringOne(String apiId, String param, String value) {
        DatapiRequest request = new DatapiRequest(apiId);
        request.putParam(param, value);
        List<JSONObject> list = DatapiClient.query(request);
        //查出为空时，list中也有一个空的JSONObject对象
        if (CollUtil.isEmpty(list) || list.get(0).size() == 0) {
            return null;
        } else {
            return String.valueOf(list.get(0).values().iterator().next());
        }
    }

    public static String queryStringOne(String apiId, Map<String, Object> params) {
        DatapiRequest request = new DatapiRequest(apiId);
        params.forEach(request::putParam);
        List<JSONObject> list = DatapiClient.query(request);
        //查出为空时，list中也有一个空的JSONObject对象
        if (CollUtil.isEmpty(list) || list.getFirst().isEmpty()) {
            return null;
        } else {
            return String.valueOf(list.getFirst().values().iterator().next());
        }
    }

    public static int queryIntegerOne(String apiId, Map<String, Object> params) {
        DatapiRequest request = new DatapiRequest(apiId);
        params.forEach(request::putParam);
        List<JSONObject> list = DatapiClient.query(request);
        //查出为空时，list中也有一个空的JSONObject对象
        if (CollUtil.isEmpty(list) || list.get(0).size() == 0) {
            return -1;
        } else {
            return (Integer) list.get(0).values().iterator().next();
        }
    }

    public static <T> Page<T> queryPage(String countApiId, String listApiId, PageReq req, Class<T> tClass) {
        Page<T> page = req.getPage(tClass);
        DatapiRequest countRequest = new DatapiRequest(countApiId);
        DatapiRequest listRequest = new DatapiRequest(listApiId);
        Map<String, Object> params = BeanUtil.beanToMap(req);
        params.put("offset", req.getOffset());
        params.put("limit", req.getLimit());
        params.forEach(countRequest::putParam);
        params.forEach(listRequest::putParam);
        List<JSONObject> counts = DatapiClient.query(countRequest);
        JSONObject countJson = counts.get(0);
        //long count = (long) countJson.values().iterator().next();
        long count = ((Integer) countJson.values().iterator().next()).longValue();
        if (count == 0) {
            page.setTotal(0);
            return page;
        }
        page.setTotal(count);
        List<JSONObject> list = DatapiClient.query(listRequest);
        page.setRecords(getList(list, tClass));
        return page;
    }

    //查询list时，总条数封装进了实体类，可不用查count的sql
    public static <T> Page<T> queryPage(String listApiId, PageReq req, Class<T> tClass) {
        Page<T> page = req.getPage(tClass);
        DatapiRequest listRequest = new DatapiRequest(listApiId);
        Map<String, Object> params = BeanUtil.beanToMap(req);
        params.put("offset", req.getOffset());
        params.put("limit", req.getLimit());
        params.forEach(listRequest::putParam);
        List<JSONObject> list = DatapiClient.query(listRequest);
        page.setRecords(getList(list, tClass));
        if (CollUtil.isEmpty(list)) {
            page.setTotal(0);
        } else {
            page.setTotal(list.get(0).getLongValue("total"));
        }
        return page;
    }

    public static <T> Page<T> queryPage(String countApiId, String listApiId, Page<T> page, BaseReq req, Class<T> tClass) {
        DatapiRequest countRequest = new DatapiRequest(countApiId);
        DatapiRequest listRequest = new DatapiRequest(listApiId);
        Map<String, Object> params = BeanUtil.beanToMap(req);
        params.put("offset", (page.getCurrent() - 1) * page.getSize());
        params.put("limit", page.getSize());
        params.forEach(countRequest::putParam);
        params.forEach(listRequest::putParam);
        List<JSONObject> counts = DatapiClient.query(countRequest);
        JSONObject countJson = counts.get(0);
        long count = ((Integer) countJson.values().iterator().next()).longValue();
        if (count == 0) {
            page.setTotal(0);
            return page;
        }
        page.setTotal(count);
        List<JSONObject> list = DatapiClient.query(listRequest);
        page.setRecords(getList(list, tClass));
        return page;
    }

    public static <T> Page<T> queryPage(String countApiId, String listApiId, PageReq req, String param, String value, Class<T> tClass) {
        Page<T> page = req.getPage(tClass);
        DatapiRequest countRequest = new DatapiRequest(countApiId);
        DatapiRequest listRequest = new DatapiRequest(listApiId);
        Map<String, Object> params = BeanUtil.beanToMap(req);
        params.put(param, value);
        params.put("offset", req.getOffset());
        params.put("limit", req.getLimit());
        params.forEach(countRequest::putParam);
        params.forEach(listRequest::putParam);
        List<JSONObject> counts = DatapiClient.query(countRequest);
        JSONObject countJson = counts.get(0);
        //long count = (long) countJson.values().iterator().next();
        long count = ((Integer) countJson.values().iterator().next()).longValue();
        if (count == 0) {
            page.setTotal(0);
            return page;
        }
        page.setTotal(count);
        List<JSONObject> list = DatapiClient.query(listRequest);
        page.setRecords(getList(list, tClass));
        return page;
    }

    /**
     * 分批查询并汇总数据返回
     */
    public static <T> Page<T> queryPageBatch(String listApiId, PageReq req, Class<T> tClass, int batchSize) {
        Page<T> resultPage = new Page<>();
        resultPage.setCurrent(req.getCurrent());
        resultPage.setSize(req.getSize());
        resultPage.setRecords(new ArrayList<>());
        if (req.getSize() < 0) {
            // size=-1查所有
            req.setCurrent(1);
            req.setSize(Integer.MAX_VALUE);
        } else if (req.getSize() <= batchSize) {
            return queryPage(listApiId, req, tClass);
        }
        int originOffset = req.getOffset();
        int originSize = req.getSize();
        req.setSize(1);
        Page<T> firstPage = queryPage(listApiId, req, tClass);
        long total = firstPage.getTotal();
        resultPage.setTotal(total);
        // 本页需要查询的实际数量
        int num = Math.min(originSize, Math.max(0, (int) total - originOffset));
        if (num == 0) {
            return resultPage;
        }
        for (int i = 1; i <= (num + batchSize - 1) / batchSize; i++) {
            int offset = originOffset + (i - 1) * batchSize;
            DatapiRequest listRequest = new DatapiRequest(listApiId);
            Map<String, Object> params = BeanUtil.beanToMap(req);
            params.put("offset", offset);
            params.put("limit", Math.min(num - (i - 1) * batchSize, batchSize));
            params.forEach(listRequest::putParam);
            List<JSONObject> list = DatapiClient.query(listRequest);
            resultPage.getRecords().addAll(getList(list, tClass));
        }
        return resultPage;
    }

    public static <T> Page<T> queryPageBatch(String listApiId, PageReq req, Class<T> tClass) {
        int batchSize = 5000;
        return queryPageBatch(listApiId, req, tClass, batchSize);
    }

    private static <T> List<T> getList(List<JSONObject> list, Class<T> tClass) {
        List<JSONObject> res = new ArrayList<>(list.size());
        list.forEach(jsonObject -> res.add(formatKey(jsonObject)));
        return JSON.parseArray(JSON.toJSONString(res), tClass, JSONReader.Feature.SupportSmartMatch);
    }

    private static <T> T getOne(List<JSONObject> list, Class<T> tClass) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        JSONObject res = formatKey(list.getFirst());
        return JSON.parseObject(JSON.toJSONString(res), tClass, JSONReader.Feature.SupportSmartMatch);
    }

    private static JSONObject formatKey(final JSONObject json) {
        JSONObject real = new JSONObject();
        for (String it : json.keySet()) {
            Object objR = json.get(it);
            String key = it.contains("_") ? StrUtil.toCamelCase(it) : it;
            if (objR instanceof JSONObject) {
                real.put(key, formatKey((JSONObject) objR));
            } else if (objR instanceof JSONArray) {
                JSONArray jsonA = new JSONArray();
                for (Object objA : (JSONArray) objR) {
                    jsonA.add(formatKey((JSONObject) objA));
                }
                real.put(key, jsonA);
            } else {
                real.put(key, objR);
            }
        }
        return real;
    }

    public static void main(String[] args) {
        String sql = "SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,\n" + "        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,\n"
                + "        SS_CE,SS_HB,\n"
                + "        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY\n"
                + "        SJXZQH_DM)*100 END ZLGXL,\n"
                + "        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY\n"
                + "        SJXZQH_DM)*100 END QLGXL,\n" + "        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF\n"
                + "        FROM(\n" + "        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,\n"
                + "        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,\n"
                + "        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,\n"
                + "        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,\n"
                + "        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,\n"
                + "        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,\n"
                + "        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB\n"
                + "        FROM (\n" + "        WITH DM_XZQH AS (\n"
                + "        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1\n"
                + "        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B\n" + "        WHERE A.XZQH_DM = B.SJXZQH_DM\n"
                + "        AND 1=1\n" + "        AND\n" + "\n" + "        (A.SJXZQH_DM IN\n"
                + "        <foreach collection=\"req.ssqh\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "            #{item}\n" + "        </foreach>\n" + "        OR A.XZQH_DM IN\n"
                + "        <foreach collection=\"req.ssqh\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "            #{item}\n" + "        </foreach>\n" + "        )\n" + "\n" + "\n" + "        )\n"
                + "        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,\n" + "        SUM(A.DAYAMT) BQ_DAY,\n"
                + "        SUM(A.MONTHAMT) BQ_MONTH,\n" + "        SUM(A.YEARAMT) BQ_YEAR,\n" + "        0 SQ_MONTH,\n"
                + "        0 SS_MONTH,\n" + "        0 SS_YEAR,\n" + "        0 YSSAMT\n"
                + "        FROM ADS_TAX_INCOME_QX_DAY A,DM_XZQH C\n" + "        WHERE A.RQ = #{req.endDate}\n" + "        AND\n"
                + "        <choose>\n" + "            <when test=\"req.zsjg != null and req.zsjg.size > 0\">\n"
                + "                A.ZSJG_DM IN\n"
                + "                <foreach collection=\"req.zsjg\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                A.ZSJG_DM='000000000000'\n" + "            </otherwise>\n"
                + "        </choose>\n" + "        AND A.XZQH_DM=C.XZQH_DM1\n" + "        AND\n" + "        <choose>\n"
                + "            <when test=\"req.ysjc != null and req.ysjc.size > 0\">\n" + "                A.YSJC_DM IN\n"
                + "                <foreach collection=\"req.ysjc\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                1=1\n" + "            </otherwise>\n" + "        </choose>\n"
                + "        AND\n" + "        <choose>\n"
                + "            <when test=\"req.yskm != null and req.yskm.size > 0\">\n" + "                A.YSKM_DM IN\n"
                + "                <foreach collection=\"req.yskm\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                1=1\n" + "            </otherwise>\n" + "        </choose>\n"
                + "        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC\n" + "        UNION ALL\n"
                + "        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,\n" + "        0 BQ_DAY,\n"
                + "        0 BQ_MONTH,\n" + "        0 BQ_YEAR,\n" + "        0 SQ_MONTH,\n"
                + "        SUM(MONTHAMT) SS_MONTH,\n" + "        SUM(YEARAMT) SS_YEAR,\n" + "        0 YSSAMT\n"
                + "        FROM ADS_TAX_INCOME_QX_MON A,DM_XZQH C\n" + "        WHERE A.RQ = #{req.endDateSq}\n"
                + "        AND\n" + "        <choose>\n"
                + "            <when test=\"req.zsjg != null and req.zsjg.size > 0\">\n" + "                A.ZSJG_DM IN\n"
                + "                <foreach collection=\"req.zsjg\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                A.ZSJG_DM='000000000000'\n" + "            </otherwise>\n"
                + "        </choose>\n" + "        AND A.XZQH_DM=C.XZQH_DM1\n" + "        AND\n" + "        <choose>\n"
                + "            <when test=\"req.ysjc != null and req.ysjc.size > 0\">\n" + "                A.YSJC_DM IN\n"
                + "                <foreach collection=\"req.ysjc\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                1=1\n" + "            </otherwise>\n" + "        </choose>\n"
                + "        AND\n" + "        <choose>\n"
                + "            <when test=\"req.yskm != null and req.yskm.size > 0\">\n" + "                A.YSKM_DM IN\n"
                + "                <foreach collection=\"req.yskm\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                1=1\n" + "            </otherwise>\n" + "        </choose>\n"
                + "        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC\n" + "        UNION ALL\n"
                + "        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,\n" + "        0 BQ_DAY,\n"
                + "        0 BQ_MONTH,\n" + "        0 BQ_YEAR,\n" + "        0 SQ_MONTH,\n" + "        0 SS_MONTH,\n"
                + "        0 SS_YEAR,\n" + "        SUM(A.YSSAMT) YSSAMT\n" + "        FROM ADS_TAX_INCOME_YSS_YEAR A,\n"
                + "        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE\n" + "        <choose>\n"
                + "            <when test=\"req.yskm != null and req.yskm.size > 0\">\n" + "                A.YSKM_DM IN\n"
                + "                <foreach collection=\"req.yskm\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                1=1\n" + "            </otherwise>\n" + "        </choose>\n"
                + "        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,\n" + "        DM_XZQH C\n"
                + "        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)\n" + "        AND A.XZQH_DM=C.XZQH_DM1\n"
                + "        AND A.YSKM_DM =B.YSKM_DM\n" + "        AND\n" + "        <choose>\n"
                + "            <when test=\"req.yskm != null and req.yskm.size > 0\">\n" + "                A.YSKM_DM IN\n"
                + "                <foreach collection=\"req.yskm\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                + "                    #{item}\n" + "                </foreach>\n" + "            </when>\n"
                + "            <otherwise>\n" + "                1=1\n" + "            </otherwise>\n" + "        </choose>\n"
                + "        AND TYPE_CODE = '1'\n" + "        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC\n"
                + "        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC\n" + "        ) A ORDER BY 1,2";
        List<String> collectionList = new ArrayList<>();
        int index = 0;
        while (sql.contains("<foreach")) {
            int index1 = sql.indexOf("<foreach");
            int index2 = sql.indexOf("</foreach>");
            String str = sql.substring(index1, index2 + 10);
            Document document = XmlUtil.parseXml(str);
            String collectionStr = document.getDocumentElement().getAttribute("collection");
            if (collectionStr.indexOf(".") > 0) {
                collectionStr = collectionStr.substring(collectionStr.indexOf(".") + 1);
            }
            String listStr = "(<#list " + collectionStr + " as item>" + "'${item}' <#if item_has_next>,</#if> </#list>)";
            collectionList.add(listStr);
            sql = sql.substring(0, index1) + "$$collectionList" + index + "$$" + sql.substring(index2 + 10);
            index++;
        }
        List<String> ifList = new ArrayList<>();
        index = 0;


        sql = chooseIndex(sql, "<choose>", "</choose>");
        while (sql.contains("<if")) {
            int index1 = sql.indexOf("<if");
            int index2 = sql.indexOf("</if>");
            String str = sql.substring(index1, index2 + 5);
            Document document = XmlUtil.parseXml(str);
            String testStr = document.getDocumentElement().getAttribute("test");
            String testValue = document.getElementsByTagName("if").item(0).getFirstChild().getNodeValue();
            if (testStr.contains("and")) {
                testStr = testStr.replace("and", "&&");
            }
            if (testStr.contains("or")) {
                testStr = testStr.replace("or", "||");
            }
            if (testStr.contains("req.")) {
                testStr = testStr.replace("req.", "");
            }
            if (testStr.contains("(")) {
                testStr = testStr.replace("(", "");
            }
            if (testStr.contains(")")) {
                testStr = testStr.replace(")", "");
            }
            if (testStr.contains(".size")) {
                testStr = testStr.replace(".size", "?size");
            }
            if (testStr.contains("null")) {
                int index3 = testStr.indexOf("null");
                String testStr1 = testStr.substring(0, index3);
                if (testStr1.contains("==")) {
                    testStr = "!" + testStr;
                    testStr = testStr.replace("==", "??");
                }
                if (testStr1.contains("!=")) {
                    testStr = testStr.replace("!=", "??");
                }
                testStr = testStr.replace("null", "");
            }
            if (testStr.contains("'")) {
                testStr = testStr.replace("'", "\"");
            }
            if (testStr.contains(".toString")) {
                testStr = testStr.replace(".toString", "");
            }
            String ifStr = "<#if " + testStr + ">" + testValue + "</#if>";
            sql = sql.substring(0, index1) + ifStr + sql.substring(index2 + 5);
        }
        for (int i = 0; i < collectionList.size(); i++) {
            sql = sql.replace("$$collectionList" + i + "$$", collectionList.get(i));
        }
        if (sql.contains("&lt;")) {
            sql = sql.replace("&lt;", "<");
        }
        if (sql.contains("&gt;")) {
            sql = sql.replace("&gt;", ">");
        }
        while (sql.contains("#{")) {
            int index1 = sql.indexOf("#{");
            int index2 = sql.substring(index1).indexOf("}");
            String string = sql.substring(index1 + 2, index1 + index2);
            if (string.contains("req.")) {
                string = string.replace("req.", "");
            }
            sql = sql.substring(0, index1) + "'${" + string + "}'" + sql.substring(index1 + index2 + 1);
        }
        System.out.println(sql);
    }

    private static String chooseSql(String sql) {
        List<String> ifList = new ArrayList<>();
        int index = 0;
        while (sql.contains("<choose>")) {
            int index1 = sql.indexOf("<choose>");
            int index2 = sql.indexOf("</choose>");
            int index22 = sql.lastIndexOf("</choose>", index2 + 100);
            String str = sql.substring(index1, index22 + 9);
            Document document = XmlUtil.parseXml(str);
            NodeList nodeList = document.getElementsByTagName("when");
            String strIf = "";
            for (int i = 0; i < nodeList.getLength(); i++) {
                String testStr = nodeList.item(i).getAttributes().getNamedItem("test").getNodeValue();
                if (testStr.contains("and")) {
                    testStr = testStr.replace("and", "&&");
                }
                if (testStr.contains("or")) {
                    testStr = testStr.replace("or", "||");
                }
                if (testStr.contains("req.")) {
                    testStr = testStr.replace("req.", "");
                }
                if (testStr.contains("(")) {
                    testStr = testStr.replace("(", "");
                }
                if (testStr.contains(")")) {
                    testStr = testStr.replace(")", "");
                }
                if (testStr.contains(".size")) {
                    String tt = testStr.substring(0, testStr.indexOf(".size"));
                    int ttIndex = tt.lastIndexOf(" ");
                    testStr = testStr.substring(0, ttIndex) + " (" + testStr.substring(ttIndex);
                    testStr = testStr.replace(".size", "?size");
                    testStr += ")";
                }
                if (testStr.contains("null")) {
                    int index3 = testStr.indexOf("null");
                    String testStr1 = testStr.substring(0, index3);
                    if (testStr1.contains("==")) {
                        testStr = "!" + testStr;
                        testStr = testStr.replace("==", "??");
                    }
                    if (testStr1.contains("!=")) {
                        testStr = testStr.replace("!=", "??");
                    }
                    testStr = testStr.replace("null", "");
                }
                if (testStr.contains("'")) {
                    testStr = testStr.replace("'", "\"");
                }
                if (testStr.contains(".toString")) {
                    testStr = testStr.replace(".toString", "");
                }
                if (i == 0) {
                    strIf += "<#if " + testStr + ">" + nodeList.item(i).getFirstChild().getNodeValue();
                } else {
                    strIf += "<#elseif " + testStr + ">" + nodeList.item(i).getFirstChild().getNodeValue();
                }
            }
            NodeList nodeList1 = document.getElementsByTagName("otherwise");
            if (nodeList1.getLength() > 0) {
                strIf += "<#else>" + nodeList1.item(0).getFirstChild().getNodeValue();
            }
            strIf += "</#if>";
            ifList.add(strIf);
            sql = sql.substring(0, index1) + strIf + sql.substring(index2 + 9);
            index++;
        }

        return sql;
    }

    private static String chooseIndex(String sql, String start, String end) {
        int index = 0;
        int endIndex = 0;
        if (!sql.contains(start)) {
            return null;
        } else {
            getIndex(sql, "<choose>", "</choose>");
            List<String> ifList = new ArrayList<>();
            TreeMap<String, String> tempMap = new TreeMap<String, String>(new Comparator<String>() {

                /*
                 * int compare(Object o1, Object o2) 返回一个基本类型的整型，
                 * 返回负数表示：o1 小于o2，
                 * 返回0 表示：o1和o2相等，
                 * 返回正数表示：o1大于o2。
                 */
                public int compare(String o1, String o2) {

                    //指定排序器按照降序排列
                    return o2.compareTo(o1);
                }
            });//指定排序器
            while (true) {
                boolean result = getIndex(sql, "<choose>", "</choose>");
                if (!result) {
                    break;
                }
                int index1 = startList.get(0);
                int index2 = endList.get(0);
                String str = sql.substring(index1, index2 + 9);
                Document document = XmlUtil.parseXml(str);
                NodeList nodeList = document.getElementsByTagName("when");
                String strIf = "";
                for (int i = 0; i < nodeList.getLength(); i++) {
                    String testStr = nodeList.item(i).getAttributes().getNamedItem("test").getNodeValue();
                    if (testStr.contains("and")) {
                        testStr = testStr.replace("and", "&&");
                    }
                    if (testStr.contains("or")) {
                        testStr = testStr.replace("or", "||");
                    }
                    if (testStr.contains("req.")) {
                        testStr = testStr.replace("req.", "");
                    }
                    if (testStr.contains("(")) {
                        testStr = testStr.replace("(", "");
                    }
                    if (testStr.contains(")")) {
                        testStr = testStr.replace(")", "");
                    }
                    if (testStr.contains(".size")) {
                        String tt = testStr.substring(0, testStr.indexOf(".size"));
                        int ttIndex = tt.lastIndexOf(" ");
                        testStr = testStr.substring(0, ttIndex) + " (" + testStr.substring(ttIndex);
                        testStr = testStr.replace(".size", "?size");
                        testStr += ")";
                    }
                    if (testStr.contains("null")) {
                        int index3 = testStr.indexOf("null");
                        String testStr1 = testStr.substring(0, index3);
                        if (testStr1.contains("==")) {
                            testStr = "!" + testStr;
                            testStr = testStr.replace("==", "??");
                        }
                        if (testStr1.contains("!=")) {
                            testStr = testStr.replace("!=", "??");
                        }
                        testStr = testStr.replace("null", "");
                    }
                    if (testStr.contains("'")) {
                        testStr = testStr.replace("'", "\"");
                    }
                    if (testStr.contains(".toString")) {
                        testStr = testStr.replace(".toString", "");
                    }
                    if (i == 0) {
                        strIf += "<#if " + testStr + ">" + nodeList.item(i).getFirstChild().getNodeValue();
                    } else {
                        strIf += "<#elseif " + testStr + ">" + nodeList.item(i).getFirstChild().getNodeValue();
                    }
                }
                NodeList nodeList1 = document.getElementsByTagName("otherwise");
                if (nodeList1.getLength() > 0) {
                    strIf += "<#else>" + nodeList1.item(0).getFirstChild().getNodeValue();
                }
                strIf += "</#if>";
                ifList.add(strIf);
                tempMap.put("temp" + index, strIf);
                sql = sql.substring(0, index1) + "temp" + index + sql.substring(index2 + 9);
                index++;
            }


            for (String key : tempMap.keySet()) {
                sql = sql.replace(key, tempMap.get(key).toString());
            }

        }
        return sql;

    }

    private static boolean getIndex(String sql, String start, String end) {
        int index = 0;
        int endIndex = 0;
        List<Integer> startTempList = new ArrayList<>();
        List<Integer> endTempList = new ArrayList<>();
        index = sql.indexOf(start, index);
        if (index != -1) {
            startTempList.add(index);
        }
        while (sql.substring(index + 1).contains(start)) {
            index = sql.indexOf(start, index + 1);
            if (index != -1) {
                startTempList.add(index);
            }
        }

        endIndex = sql.indexOf(end, endIndex);
        if (endIndex != -1) {
            endTempList.add(endIndex);
        }
        while (sql.substring(endIndex + 1).contains(end)) {
            endIndex = sql.indexOf(end, endIndex + 1);
            if (endIndex != -1) {
                endTempList.add(endIndex);
            }

        }

        List<Integer> changeList = new ArrayList<>();
        for (int i = 0; i < startTempList.size(); i++) {
            if (i + 1 == startTempList.size()) {
                break;
            }
            int startValue = startTempList.get(i + 1);
            int endValue = endTempList.get(i);
            if (startValue < endValue) {
                changeList.add(i);
            }
        }

        for (int k = 0; k < changeList.size(); k++) {
            int changeIndex = changeList.get(k);
            Collections.swap(startTempList, changeIndex, changeIndex + 1);
        }

        startList = new ArrayList<>(startTempList);
        endList = new ArrayList<>(endTempList);

        return startList.size() > 0;

    }


}
