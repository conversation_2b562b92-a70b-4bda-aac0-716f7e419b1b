package com.zjhh.comm.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

import java.io.File;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/8/21 上午8:58
 */
public class FileUtils {

    private static final String AES_KEY = "d457853b8d8384cd4c2fbe3f2e995b6c";

    private static final AES aes = SecureUtil.aes(AES_KEY.getBytes());

    public static String createFileDir(String filename) {
        return createPath() + File.separator + System.currentTimeMillis() + "." + FileUtil.getSuffix(filename);
    }

    public static String createPath() {
        LocalDate now = LocalDate.now();
        return now.getYear() + File.separator + now.getMonthValue() + File.separator + now.getDayOfMonth();
    }

    public static String encryptDocId(String docId) {
        if (docId == null) {
            return null;
        }
        byte[] bytes = aes.encrypt(Long.toString(Long.parseLong(docId), 36) + RandomUtil.randomString(1));
        return Base64.encodeUrlSafe(bytes);
    }

    public static String decryptDocId(String encryptDocId) {
        if (encryptDocId == null) {
            return null;
        }
        String str = aes.decryptStr(encryptDocId);
        return String.valueOf(Long.parseLong(str.substring(0, str.length() - 1), 36));
    }

    public static void main(String[] args) {
        System.out.println(encryptDocId("1847093146898710528"));
        System.out.println(decryptDocId("vF1IVsqqvuboDRp8ElRd_w"));

    }
}
