package com.zjhh.comm.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-06-06 4:04 下午
 */
public class SQLConvertDataApiUtils {

    public static void main(String[] args) {

        ConvertVo convertVo = new ConvertVo();
        convertVo.setXzqh("A.SJXZQH_DM");
        convertVo.setZsjg("A.GDSBS& ");
        convertVo.setSzjj("CYLX_DM &CYLX_DM = 'S001'");
        convertVo.setHydm("A.SJHY_DM& ");

        convertVo.setYskmStr("A.SJYSKM_DM = 'AA101' & A.SJYSKM_DM = '${yskm}'");
        convertVo.setYskm1Str("CONCAT('G',A.SJYSKM_DM) = 'A'&CONCAT('G',A.SJYSKM_DM) =  '${yskm}'");
        convertVo.setTsxzList("A.LB_DM ");
        String zb = "<#if zb = '11'>\n" + " T.QYSL\n" + " <#elseif zb = '12'>\n" + "T.CYRS\n" + "  <#elseif zb = '13'>\n"
                + " T.SBF\n" + "  <#elseif zb = '21'>\n" + " T.YYSR\n" + "  <#elseif zb = '22'>\n" + " T.AMT\n"
                + "  <#elseif zb = '23'>\n" + "T.CITY_AMT+T.COUNTY_AMT\n" + "  <#elseif zb = '31'>\n" + " T.XGMNSR\n"
                + "  <#elseif zb = '32'>\n" + " T.YBNSR\n" + "  <#elseif zb = '33'>\n" + " T.XZYB\n" + "  <#elseif zb = '34'>\n"
                + " T.GSQYSL\n" + "  <#elseif zb = '35'>\n" + " T.ZCZB\n" + "  <#elseif zb = '41'>\n" + " T.QYFC\n"
                + " <#else>\n" + "T.TS\n" + " </#if> ";

        Map<String, String> map = new HashMap<>();
        map.put("'||S_YSKM||'", convertVo.getYskmStr());
        map.put("'||S_YSKM1||'", convertVo.getYskm1Str());
        map.put("'||S_YSJC||'", convertVo.getYsjcStr());
        map.put("'||V_START_DATE||'", convertVo.getStartDate());
        map.put("'||V_END_DATE||'", convertVo.getEndDate());
        map.put("'||S_TSXZ||'", convertVo.getTsxzList());
        map.put("'||S_DATE_SQ1||'", convertVo.getDateSq1());
        map.put("'||S_DATE_SQ2||'", convertVo.getDateSq2());
        map.put("'||S_START_DT_SQ||'", convertVo.getStartDtSq());
        map.put("'||S_END_DT_SQ||'", convertVo.getEndDtSq());

        map.put("'||V_XZLX||'", convertVo.getXzlxStr());
        map.put("'||V_TSXZ||'", "${tsxz}");
        map.put("'||S_ZB||'", zb);
        map.put("'||S_DATE_1||'", convertVo.getDate1Str());
        map.put("'||S_DATE_2||'", convertVo.getDate2Str());
        map.put("'||S_DATE_3||'", convertVo.getDate3Str());
        map.put("'||S_DATE_4||'", convertVo.getDate4Str());
        map.put("'|| S_BBQ || '", "('${date1}','${date2}','${date3}','${date4}')");
        map.put("'||BQ_MONTH||'", "${bqMonth}");
        map.put("'||SQ_MONTH||'", "${sqMonth}");
        map.put("'||S_YEAR||'", "${sYear}");
        map.put("'||V_YEAR||'", "${year}");
        map.put("'||S_HYDM||'", convertVo.getHydm());
        map.put("'||S_SZJJ||'", convertVo.getSzjj());

//		map.put("'||S_YSKM||'","<#if yskm?? && yskm?size > 0> YSKM_DM IN (<#list tsxz as item> '${item}' <#if item_has_next>,</#if> </#list>) <#else>"
//				+ "YSKM_DM  LIKE 'AA%'" + "</#if>");
        map.put("'||S_XZQH||'", convertVo.getXzqh());
        map.put("'||V_TYPE||'", "${type}");
        map.put("'||S_YSJC||'", "CASE WHEN '${ysjc}' ='NUL' THEN SUM(B.AMT) \n"
                + "                   WHEN '${ysjc}' = '1' THEN SUM(B.CENT_AMT) \n"
                + "                   WHEN '${ysjc}' = '2' THEN SUM(B.CITY_AMT) \n"
                + "                   WHEN '${ysjc}' = '3,4,5' THEN SUM(B.COUNTY_AMT)\n"
                + "                   WHEN '${ysjc}' = '1,3,4,5' THEN SUM(B.CENT_AMT)+SUM(B.COUNTY_AMT)\n"
                + "                   WHEN '${ysjc}' = '2,3,4,5' THEN SUM(B.CITY_AMT)+SUM(B.COUNTY_AMT)\n"
                + "                   WHEN '${ysjc}' = '1,2' THEN SUM(B.CITY_AMT)+SUM(B.CENT_AMT)\n" + "               END");


        String sql = "SELECT A.*,\n" + "       CASE WHEN SQ_AMT = 0 THEN 0 ELSE ROUND((BQ_AMT/SQ_AMT-1)*100,2) END AS ZF,\n"
                + "       CASE WHEN SUM(BQ_AMT)OVER(PARTITION BY A.PNODE_CODE) = 0 THEN 0 \n"
                + "     ELSE ROUND(BQ_AMT/SUM(BQ_AMT)OVER(PARTITION BY A.PNODE_CODE)*100,2) END ZB,\n"
                + "       CASE WHEN LENGTH(NODE_CODE)=6 THEN 1 ELSE 0 END ISLEAF\n" + "    FROM (    \n"
                + "    SELECT A.PNODE_CODE,A.NODE_CODE,A.NODE_NAME, A.CHOICE_CODE,A.CHOICE_NAME, SUM(BQ_AMT) AS BQ_AMT, SUM(SQ_AMT) AS SQ_AMT\n"
                + "    FROM (    \n" + "    WITH DATA1 AS (\n" + "    SELECT B.XZQH_DM,B.HY_DM,\n"
                + "       SUM(CASE WHEN B.RQ BETWEEN '''||V_START_DATE||''' AND '''||V_END_DATE||''' THEN '||S_YSJC||' ELSE 0 END) BQ_AMT,\n"
                + "       SUM(CASE WHEN B.RQ BETWEEN '''||S_START_DT_SQ||''' AND '''||S_END_DT_SQ||''' THEN '||S_YSJC||' ELSE 0 END) SQ_AMT \n"
                + "    FROM (SELECT * FROM DM_GY_SZJJ D WHERE '||S_SZJJ||') A\n" + "    INNER JOIN (\n"
                + "    SELECT  B.RQ,B.HY_DM,B.XZQH_DM,\n" + "    SUM(AMT) AS AMT ,SUM(CENT_AMT) AS CENT_AMT,\n"
                + "    SUM(CITY_AMT) AS CITY_AMT ,SUM(COUNTY_AMT) AS COUNTY_AMT,0 AS QX_AMT\n" + "    FROM ADS_SAT_QYXX_MON B\n"
                + "    WHERE B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH_ZQ A WHERE '||S_XZQH||')    \n"
                + "      AND  B.RQ BETWEEN '''||V_START_DATE||''' AND '''||V_END_DATE||'''\n"
                + "      AND B.YSKM_DM LIKE ''AA101%'' \n" + "      AND B.YSKM_DM NOT LIKE ''AA1010103%'' \n"
                + "    GROUP BY B.RQ,B.HY_DM,B.XZQH_DM\n" + "    ) B ON A.HY_DM = B.HY_DM\n"
                + "    GROUP BY B.XZQH_DM,B.HY_DM\n" + "    UNION ALL \n" + "    SELECT B.XZQH_DM,B.HY_DM,\n"
                + "       SUM(CASE WHEN B.RQ BETWEEN '''||V_START_DATE||''' AND '''||V_END_DATE||''' THEN '||S_YSJC||' ELSE 0 END) BQ_AMT,\n"
                + "       SUM(CASE WHEN B.RQ BETWEEN '''||S_START_DT_SQ||''' AND '''||S_END_DT_SQ||''' THEN '||S_YSJC||' ELSE 0 END) SQ_AMT \n"
                + "    FROM (SELECT * FROM DM_GY_SZJJ D WHERE '||S_SZJJ||') A\n"
                + "    LEFT JOIN ADS_SAT_HZXX_QX_MON B ON A.HY_DM = B.HY_DM \n"
                + "    WHERE B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH_ZQ A WHERE '||S_XZQH||')\n"
                + "    AND  (B.RQ BETWEEN '''||V_START_DATE||''' AND '''||V_END_DATE||'''  OR B.RQ BETWEEN '''||S_START_DT_SQ||''' AND '''||S_END_DT_SQ||''')\n"
                + "    AND B.YSKM_DM LIKE ''AA101%'' \n" + "    AND B.YSKM_DM NOT LIKE ''AA1010103%'' \n"
                + "    GROUP BY B.XZQH_DM,B.HY_DM\n" + "    ),\n" + "    DM_HY AS( \n"
                + "    SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC, B.HY_DM HY_DM1\n" + "    FROM DM_GY_HY A, DM_GY_HY_ZQ B\n"
                + "    WHERE A.HY_DM = B.SJHY_DM\n" + "      AND '||S_HYDM||'\n" + "    ),\n" + "    DM_XZQH AS( \n"
                + "    SELECT A.SJXZQH_DM, A.SJXZQH_MC,A.XZQH_DM, A.XZQH_MC, B.XZQH_DM XZQH_DM1\n"
                + "    FROM DM_CZ_XZQH A, DM_CZ_XZQH_ZQ B\n" + "    WHERE A.XZQH_DM = B.SJXZQH_DM\n" + "     AND '||S_XZQH||'\n"
                + "    )\n" + "    SELECT  B.SJHY_DM AS CHOICE_CODE,  B.SJHY_MC  CHOICE_NAME, \n"
                + "    C.SJXZQH_DM AS PNODE_CODE, C.XZQH_DM  AS NODE_CODE, C.XZQH_MC AS NODE_NAME, \n"
                + "    SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) AS SQ_AMT\n" + "    FROM DATA1 A , DM_HY B, DM_XZQH C\n"
                + "    WHERE A.HY_DM = B.HY_DM1\n" + "    AND A.XZQH_DM = C.XZQH_DM1\n"
                + "    GROUP BY C.SJXZQH_DM,B.SJHY_DM ,  B.SJHY_MC,C.XZQH_DM,C.XZQH_MC \n" + "    UNION ALL \n"
                + "    SELECT  DISTINCT B.SJHY_DM AS CHOICE_CODE,  B.SJHY_MC  CHOICE_NAME, \n"
                + "    B.SJXZQH_DM AS PNODE_CODE, C.XZQH_DM  AS NODE_CODE, C.XZQH_MC AS NODE_NAME, \n"
                + "    0 AS BQ_AMT,0 AS SQ_AMT\n" + "    FROM DM_HY B, DM_XZQH C\n" + "    ) A \n"
                + "    GROUP BY A.PNODE_CODE,A.NODE_CODE,A.NODE_NAME, A.CHOICE_CODE,A.CHOICE_NAME\n" + "    ) A \n"
                + "    ORDER BY CHOICE_CODE,NODE_CODE";

        sql = sql.replace("''", "'");
        for (String s : map.keySet()) {
            sql = sql.replace(s, map.get(s));
        }

        System.out.println(sql);
    }


}
