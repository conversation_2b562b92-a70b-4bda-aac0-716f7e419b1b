package com.zjhh.comm.utils;

import cn.hutool.core.io.FileUtil;
import com.zjhh.comm.constants.FileConstants;
import com.zjhh.comm.dto.ResourceDto;
import com.zjhh.comm.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import jakarta.servlet.http.HttpServletResponse;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;

/**
 * <AUTHOR>
 * @since 2023/6/2 10:14
 */
@Slf4j
public class FilePreviewUtil {

    public static ResourceDto preview(String inPath, String filename) {
        ResourceDto dto = new ResourceDto();
        File file = new File(inPath);
        if (!file.exists()) {
            throw new BizException("该文件不存在或已删除！");
        }
        String originalSuffix = FileUtil.getSuffix(file);
        String suffix = originalSuffix.toLowerCase();
        if (FileConstants.CAN_PREVIEW_DOC_TYPE.contains(suffix)) {
            dto.setFileName(filename);
            dto.setFilePath(inPath);
            return dto;
        }
        if (FileConstants.CANNOT_PREVIEW_DOC_TYPE.contains(suffix)) {
            throw new BizException("该文件不支持预览！");
        }
        if (FileConstants.PREVIEW_PDF_DOC_TYPE.contains(suffix)) {
            String filePath = inPath.replace("." + originalSuffix, ".pdf");
            File pdf = new File(filePath);
            if (!pdf.exists()) {
                AsposeUtil.convertToPdf(suffix, inPath, pdf);
            }
            dto.setFilePath(filePath);
            dto.setFileName(filename);
        }
        return dto;
    }

    public static void generatePdfPreview(Path pdfPath, HttpServletResponse response) {
        try (FileInputStream fis = new FileInputStream(pdfPath.toFile());
             PDDocument document = PDDocument.load(fis)) {

            // 加载 PDF 文档
            PDFRenderer pdfRenderer = new PDFRenderer(document);

            // 渲染指定页为 BufferedImage
            BufferedImage image = pdfRenderer.renderImageWithDPI(0, 150f);

            // 设置响应内容类型
            response.setContentType("image/jpeg");
            response.setHeader("Content-Disposition", "inline; filename=preview.jpg");

            // 将图像写入响应输出流
            ImageIO.write(image, "JPEG", response.getOutputStream());
        } catch (IOException e) {
            log.error("生成PDF预览失败", e);
            throw new BizException("生成PDF预览失败");
        }
    }

}
