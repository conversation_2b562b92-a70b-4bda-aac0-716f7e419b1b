package com.zjhh.comm.utils;

import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Arrays;
import java.util.List;

/**
 * 转换VO
 *
 * <AUTHOR>
 * @date 2022-06-06 4:13 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConvertVo extends BaseVo {
    @Serial
    private static final long serialVersionUID = -7131447169752041369L;

    private String yskmStr;

    private String yskmList;

    private String yskm1Str;

    private String ysjcStr;

    private String startDate;

    private String endDate;

    private String tsxzList;

    private String dateSq1;

    private String startDtSq;

    private String endDtSq;


    private String dateSq2;

    private String xzlxStr;

    private String date1Str;
    private String date2Str;
    private String date3Str;
    private String date4Str;
    private String xzqh;

    private String xzqhStr;


    private String zsjg;

    private String hydm;

    private String szjj;


    public String getYskmStr() {

        if (StrUtil.isBlank(yskmStr)) return "";
        List<String> yskms = Arrays.asList(yskmStr.split("&"));

        return "<#if yskm ?? && yskm != ''>" + yskms.get(0) + "<#else>"
                + (yskms.size() > 1 ? yskms.get(1) : "") + "</#if>";
    }

    public String getYskmList() {
        return "<#if yskm ?? && (yskm.size > 0)>" +
                yskmList + "IN (<#list yskm as item> '${item}' <#if item_has_next>,</#if> </#list>)"
                + "</#if>";
    }

    public String getYskm1Str() {
        if (StrUtil.isBlank(yskm1Str)) return "";
        List<String> yskms = Arrays.asList(yskm1Str.split("&"));
        return "<#if yskm ?? && yskm != ''>" + yskms.get(0) + "<#else>"
                + (yskms.size() > 1 ? yskms.get(1) : "") + "</#if>";
    }

    public String getYsjcStr() {
        return "<#if ysjc = '1'> "
                + "cent_amt"
                + "<#elseif ysjc = '2'>"
                + "city_amt"
                + "<#elseif ysjc = '3,4,5'>"
                + "county_amt"
                + "<#else>"
                + "amt"
                + "</#if>";
    }

    public String getStartDate() {
        return "${startDate}";
    }

    public String getEndDate() {
        return "${endDate}";
    }

    public String getTsxzList() {
        return "<#if tsxz ?? && (tsxz?size > 0)>" +
                tsxzList + "IN (<#list tsxz as item> '${item}' <#if item_has_next>,</#if> </#list>)"
                + "<#else>"
                + " 1=1"
                + "</#if>";
    }

    public String getDateSq1() {
        return "${dateSq1}";
    }

    public String getDateSq2() {
        return "${dateSq2}";
    }

    public String getXzlxStr() {
        return "${xzlx}";
    }

    public String getDate1Str() {
        return "${date1}";
    }

    public String getDate2Str() {
        return "${date2}";
    }

    public String getDate3Str() {
        return "${date3}";
    }

    public String getDate4Str() {
        return "${date4}";
    }

    public String getXzqh() {

        return "<#if xzqh ?? && (xzqh?size > 0)>" +
                xzqh + " IN (<#list xzqh as item> '${item}' <#if item_has_next>,</#if> </#list>)"
                + "<#else>"
                + "1=1"
                + "</#if>";
    }

    public String getXzqhStr() {
        return "<#if xzqh ??>" +
                "A.XZQH_DM IN (SELECT DISTINCT XZQH_DM FROM DM_CZ_XZQH_ZQ WHERE SJXZQH_DM = '${xzqh}')"
                + "<#else>" + "1=1" + "</#if>";
    }

    public String getZsjg() {
        List<String> params = Arrays.asList(zsjg.split("&"));

        return "<#if zsjg ?? && zsjg?size > 0>" +
                params.get(0) + " IN (<#list zsjg as item> '${item}' <#if item_has_next>,</#if> </#list>)"
                + "<#else>" + (params.get(1).equals(" ") ? "1=1" : params.get(1)) + "</#if>";
    }

    public String getHydm() {

        List<String> params = Arrays.asList(hydm.split("&"));
        return "<#if hydm ?? && (hydm?size > 0)>" +
                params.get(0) + " IN (<#list hydm as item> '${item}' <#if item_has_next>,</#if> </#list>)"
                + "<#else> " + (params.get(1).equals(" ") ? "1=1" : params.get(1)) + " </#if>";
    }

    public String getSzjj() {
        List<String> params = Arrays.asList(szjj.split("&"));
        return "<#if szjj ?? && szjj?size > 0>" +
                params.get(0) + " IN (<#list szjj as item> '${item}' <#if item_has_next>,</#if> </#list>)"
                + "<#else> " + (params.get(1).equals(" ") ? "1=1" : params.get(1)) + "</#if>";
    }

    public String getStartDtSq() {
        return "${startDtSq}";
    }

    public String getEndDtSq() {
        return "${endDtSq}";
    }
}
