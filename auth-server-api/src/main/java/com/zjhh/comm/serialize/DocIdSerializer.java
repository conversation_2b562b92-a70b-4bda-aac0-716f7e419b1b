package com.zjhh.comm.serialize;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zjhh.comm.utils.FileUtils;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024/10/11 上午9:23
 */
public class DocIdSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StrUtil.isNotBlank(value)) {
            gen.writeString(FileUtils.encryptDocId(value));
        } else {
            gen.writeString(value);
        }
    }
}
