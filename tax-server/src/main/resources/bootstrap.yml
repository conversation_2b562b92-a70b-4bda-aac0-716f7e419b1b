server:
    servlet:
        context-path: /tax_server
jasypt:
    encryptor:
        algorithm: PBEWithMD5AndDES
        iv-generator-classname: org.jasypt.iv.NoIvGenerator
spring:
    application:
        name: tax-server
    cloud:
        nacos:
            server-addr: 192.168.1.206:31048
            config:
                namespace: temp
                file-extension: yaml
                prefix: tax-server
                group: DEFAULT_GROUP
            discovery:
                namespace: ${spring.cloud.nacos.config.namespace}
                group: DEFAULT_GROUP
                enabled: true
    config:
        import: optional:nacos:tax-server