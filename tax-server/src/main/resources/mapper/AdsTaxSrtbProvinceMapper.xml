<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.tax.datamanagement.dao.mapper.AdsTaxSrtbProvinceMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , datekey, batch_no, state, create_time
    </sql>
    <update id="updateState">
        update ads_tax_srtb_province
        set state = #{state}
        where datekey = #{datekey}
          and batch_no = #{batchNo}
    </update>

    <select id="page" resultType="com.zjhh.tax.datamanagement.vo.srfsdwcqk.SrfsdwcqkVo">
        select id, datekey, batch_no, state, update_time
        from ads_tax_srtb_province
        <where>
            <if test="req.tabType == 1">
                datekey between #{req.startDate} and #{req.endDate}
            </if>
            <if test="req.tabType == 2">
                to_char(update_time, 'yyyyMM') between #{req.startDate} and #{req.endDate}
            </if>
            <if test="req.reportState == 1">
                and state = 0
            </if>
            <if test="req.reportState == 2">
                and state = 10
            </if>
            <if test="req.reportState == 3">
                and state = 99
            </if>
        </where>
        <if test="req.tabType == 1">
            order by datekey desc, batch_no desc
        </if>
        <if test="req.tabType == 2">
            order by update_time desc
        </if>
    </select>

    <select id="callAdsTaxSrtbProvinceAnnex" statementType="CALLABLE" resultType="String">
        {call p_ads_tax_srtb_province_annex(#{datekey, mode=IN, jdbcType=VARCHAR})}
    </select>

</mapper>
