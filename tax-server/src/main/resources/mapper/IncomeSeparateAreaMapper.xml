<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.income.dao.mapper.IncomeSeparateAreaMapper">
    <select id="selectIncomeSeparateAreaStructProvince" resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaStructVO">
        SELECT RG_CODE, RG_NAME,
        ROUND(SUM(BQ_DAYAMT)/10000,0) BQ_AMT,
        ROUND(SUM(SQ_DAYAMT)/10000,0) SQ_AMT,
        SUM(YSSAMT) YSSAMT,
        CASE WHEN SUM(SQ_DAYAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(SQ_DAYAMT)-1)*100,2) END ZF,
        CASE WHEN SUM(YSSAMT)=0 THEN 0 ELSE ROUND((SUM(BQ_DAYAMT)/SUM(YSSAMT))*100,2) END YSJD
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.province == '1'.toString()">
                (A.SJXZQH_DM = '3300' AND LENGTH(A.xzqh_dm) = '4')
            </when>
            <otherwise>
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        )
        SELECT B.XZQH_DM RG_CODE,B.XZQH_MC RG_NAME,
        SUM(YEARAMT) BQ_DAYAMT, 0 SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_DAY A ,DM_XZQH B
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND A.XZQH_DM = B.XZQH_DM1
        GROUP BY B.XZQH_DM,B.XZQH_MC
        UNION ALL
        SELECT B.XZQH_DM RG_CODE,B.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, SUM(YEARAMT) SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_MON A ,DM_XZQH B
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND A.XZQH_DM = B.XZQH_DM1
        GROUP BY B.XZQH_DM,B.XZQH_MC
        UNION ALL
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, 0 SQ_DAYAMT,SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B , DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.YSKM_DM =B.YSKM_DM
        AND TYPE_CODE = '0'
        AND A.XZQH_DM = C.XZQH_DM1
        GROUP BY C.XZQH_DM, C.XZQH_MC
        ) A
        GROUP BY RG_CODE, RG_NAME
        ORDER BY 3 DESC
    </select>
    <select id="selectIncomeSeparateAreaStructDistrict" resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaStructVO">
        SELECT RG_CODE, RG_NAME,
        ROUND(SUM(BQ_DAYAMT)/10000,0) BQ_AMT,
        ROUND(SUM(SQ_DAYAMT)/10000,0) SQ_AMT,
        SUM(YSSAMT) YSSAMT,
        CASE WHEN SUM(SQ_DAYAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(SQ_DAYAMT)-1)*100,2) END ZF,
        CASE WHEN SUM(YSSAMT)=0 THEN 0 ELSE ROUND((SUM(BQ_DAYAMT)/SUM(YSSAMT))*100,2) END YSJD
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND

        A.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        )
        SELECT B.XZQH_DM RG_CODE,B.XZQH_MC RG_NAME,
        SUM(YEARAMT) BQ_DAYAMT, 0 SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_QX_DAY A ,DM_XZQH B
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND A.XZQH_DM = B.XZQH_DM1
        GROUP BY B.XZQH_DM,B.XZQH_MC
        UNION ALL
        SELECT B.XZQH_DM RG_CODE,B.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, SUM(YEARAMT) SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A ,DM_XZQH B
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND A.XZQH_DM = B.XZQH_DM1
        GROUP BY B.XZQH_DM,B.XZQH_MC
        UNION ALL
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, 0 SQ_DAYAMT,SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B , DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.YSKM_DM =B.YSKM_DM
        AND TYPE_CODE = '1'
        AND A.XZQH_DM = C.XZQH_DM1
        GROUP BY C.XZQH_DM, C.XZQH_MC
        ) A
        GROUP BY RG_CODE, RG_NAME
        ORDER BY 3 DESC
    </select>
    <select id="selectIncomeSeparateAreaStructCityDistrict"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaStructVO">
        SELECT RG_CODE, RG_NAME,
        ROUND(SUM(BQ_DAYAMT)/10000,0) BQ_AMT,
        ROUND(SUM(SQ_DAYAMT)/10000,0) SQ_AMT,
        SUM(YSSAMT) YSSAMT,
        CASE WHEN SUM(SQ_DAYAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(SQ_DAYAMT)-1)*100,2) END ZF,
        CASE WHEN SUM(YSSAMT)=0 THEN 0 ELSE ROUND((SUM(BQ_DAYAMT)/SUM(YSSAMT))*100,2) END YSJD
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND

        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        )
        SELECT B.XZQH_DM RG_CODE,B.XZQH_MC RG_NAME,
        SUM(YEARAMT) BQ_DAYAMT, 0 SQ_DAYAMT,0 YSSAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A ,DM_XZQH B
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND A.XZQH_DM = B.XZQH_DM1
        GROUP BY B.XZQH_DM,B.XZQH_MC
        UNION ALL
        SELECT B.XZQH_DM RG_CODE,B.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, SUM(YEARAMT) SQ_DAYAMT,0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A ,DM_XZQH B
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND A.XZQH_DM = B.XZQH_DM1
        GROUP BY B.XZQH_DM,B.XZQH_MC
        UNION ALL
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, 0 SQ_DAYAMT,SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B , DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.YSKM_DM =B.YSKM_DM
        AND TYPE_CODE IN ('1','2')
        AND A.XZQH_DM = C.XZQH_DM1
        GROUP BY C.XZQH_DM, C.XZQH_MC
        ) A
        GROUP BY RG_CODE, RG_NAME
        ORDER BY RG_CODE ASC
    </select>
    <select id="selectIncomeSeparateAreaLineStructProvince"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaStructVO">
        SELECT RG_CODE, RG_NAME,
        SUM(BQ_DAYAMT) BQ_AMT,
        SUM(SQ_DAYAMT) SQ_AMT,SUM(YSSAMT) YSSAMT,
        CASE WHEN SUM(SQ_DAYAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(SQ_DAYAMT)-1)*100,2) END ZF,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(YSSAMT))*100,2) END YSJD
        FROM (
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        SUM(YEARAMT) BQ_DAYAMT, 0 SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_DAY A,DM_CZ_XZQH_ZQ C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.XZQH_DM , C.XZQH_MC
        UNION ALL
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, SUM(YEARAMT) SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_CZ_XZQH_ZQ C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.XZQH_DM , C.XZQH_MC
        UNION ALL
        SELECT
        C.XZQH_DM RG_CODE,
        C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, 0 SQ_DAYAMT,SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        dm_cz_xzqh_zq C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND TYPE_CODE = '0'
        AND A.YSKM_DM =B.YSKM_DM
        AND A.XZQH_DM = C.XZQH_DM
        AND
        C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY C.XZQH_DM,C.XZQH_MC
        ) A
        GROUP BY RG_CODE, RG_NAME
        ORDER BY 3 DESC
    </select>
    <select id="selectIncomeSeparateAreaLineStructDistrict"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaStructVO">
        SELECT RG_CODE, RG_NAME,
        SUM(BQ_DAYAMT) BQ_AMT,
        SUM(SQ_DAYAMT) SQ_AMT,SUM(YSSAMT) YSSAMT,
        CASE WHEN SUM(SQ_DAYAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(SQ_DAYAMT)-1)*100,2) END ZF,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(YSSAMT))*100,2) END YSJD
        FROM (
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        SUM(YEARAMT) BQ_DAYAMT, 0 SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_CZ_XZQH_ZQ C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.XZQH_DM , C.XZQH_MC
        UNION ALL
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, SUM(YEARAMT) SQ_DAYAMT,0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_CZ_XZQH_ZQ C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.XZQH_DM , C.XZQH_MC
        UNION ALL
        SELECT
        C.XZQH_DM RG_CODE,
        C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, 0 SQ_DAYAMT,SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        dm_cz_xzqh_zq C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND TYPE_CODE = '0'
        AND A.YSKM_DM =B.YSKM_DM
        AND A.XZQH_DM = C.XZQH_DM
        AND

        C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY C.XZQH_DM,C.XZQH_MC
        ) A
        GROUP BY RG_CODE, RG_NAME
        ORDER BY 3 DESC
    </select>
    <select id="selectIncomeSeparateAreaLineStructCityDistrict"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaStructVO">
        SELECT RG_CODE, RG_NAME,
        SUM(BQ_DAYAMT) BQ_AMT,
        SUM(SQ_DAYAMT) SQ_AMT,SUM(YSSAMT) YSSAMT,
        CASE WHEN SUM(SQ_DAYAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(SQ_DAYAMT)-1)*100,2) END ZF,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_DAYAMT)/SUM(YSSAMT))*100,2) END YSJD
        FROM (
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        SUM(YEARAMT) BQ_DAYAMT, 0 SQ_DAYAMT,0 YSSAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_CZ_XZQH_ZQ C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.XZQH_DM , C.XZQH_MC
        UNION ALL
        SELECT C.XZQH_DM RG_CODE,C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, SUM(YEARAMT) SQ_DAYAMT,0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_CZ_XZQH_ZQ C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.XZQH_DM , C.XZQH_MC
        UNION ALL
        SELECT
        C.XZQH_DM RG_CODE,
        C.XZQH_MC RG_NAME,
        0 BQ_DAYAMT, 0 SQ_DAYAMT,SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        dm_cz_xzqh_zq C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND TYPE_CODE = '0'
        AND A.YSKM_DM =B.YSKM_DM
        AND A.XZQH_DM = C.XZQH_DM
        AND
        C.SJXZQH_DM IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY C.XZQH_DM,C.XZQH_MC
        ) A
        GROUP BY RG_CODE, RG_NAME
        ORDER BY 3 DESC
    </select>
    <select id="selectIncomeSeparateAreaTableBeginProvince"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaTableVO">
        SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,
        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,
        SS_CE,SS_HB,
        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY
        SJXZQH_DM)*100 END ZLGXL,
        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY
        SJXZQH_DM)*100 END QLGXL,
        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM(
        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,
        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,
        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,
        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,
        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="req.province == '1'.toString()">
                        (A.SJXZQH_DM IS NULL)
                    </when>
                    <otherwise>
                        (A.SJXZQH_DM IN
                        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR A.XZQH_DM IN
                        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </otherwise>
                </choose>

            </otherwise>
        </choose>

        )
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        SUM(A.DAYAMT) BQ_DAY,
        SUM(A.MONTHAMT) BQ_MONTH,
        SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_DAY A,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        SUM(MONTHAMT) SS_MONTH,
        SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_XZQH C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.XZQH_DM=C.XZQH_DM1
        AND A.YSKM_DM =B.YSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND TYPE_CODE = '0'
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC
        ) A ORDER BY 1,2
    </select>
    <select id="selectIncomeSeparateAreaTableBeginDistrict"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaTableVO">
        SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,
        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,
        SS_CE,SS_HB,
        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY
        SJXZQH_DM)*100 END ZLGXL,
        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY
        SJXZQH_DM)*100 END QLGXL,
        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM(
        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,
        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,
        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,
        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,
        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND

        (A.SJXZQH_DM IN
        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR A.XZQH_DM IN
        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )


        )
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        SUM(A.DAYAMT) BQ_DAY,
        SUM(A.MONTHAMT) BQ_MONTH,
        SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        SUM(MONTHAMT) SS_MONTH,
        SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_XZQH C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.XZQH_DM=C.XZQH_DM1
        AND A.YSKM_DM =B.YSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND TYPE_CODE = '1'
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC
        ) A ORDER BY 1,2
    </select>
    <select id="selectIncomeSeparateAreaTableProvince" resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaTableVO">
        SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,
        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,
        SS_CE,SS_HB,
        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY
        SJXZQH_DM)*100 END ZLGXL,
        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY
        SJXZQH_DM)*100 END QLGXL,
        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM(
        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,
        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,
        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,
        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,
        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="req.province == '1'.toString()">
                        (A.SJXZQH_DM IS NULL)
                    </when>
                    <otherwise>
                        (A.SJXZQH_DM IN
                        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR A.XZQH_DM IN
                        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </otherwise>
                </choose>

            </otherwise>
        </choose>
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        - SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_DAY A,DM_XZQH C
        WHERE A.RQ = #{req.startDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_DAY A,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        - SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_XZQH C
        WHERE A.RQ = #{req.startDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_XZQH C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.XZQH_DM=C.XZQH_DM1
        AND A.YSKM_DM =B.YSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND TYPE_CODE = '0'
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC
        ) A ORDER BY 1,2 ASC
    </select>
    <select id="selectIncomeSeparateAreaTableDistrict" resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaTableVO">
        SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,
        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,
        SS_CE,SS_HB,
        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY
        SJXZQH_DM)*100 END ZLGXL,
        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY
        SJXZQH_DM)*100 END QLGXL,
        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM(
        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,
        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,
        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,
        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,
        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND

        (A.SJXZQH_DM IN
        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR A.XZQH_DM IN
        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        - SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_XZQH C
        WHERE A.RQ = #{req.startDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        - SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_XZQH C
        WHERE A.RQ = #{req.startDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_XZQH C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.XZQH_DM=C.XZQH_DM1
        AND A.YSKM_DM =B.YSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND TYPE_CODE = '1'
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC
        ) A ORDER BY 1,2 ASC
    </select>
    <select id="selectIncomeSeparateAreaTableCityDistrict"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaTableVO">
        SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,
        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,
        SS_CE,SS_HB,
        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY
        SJXZQH_DM)*100 END ZLGXL,
        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY
        SJXZQH_DM)*100 END QLGXL,
        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM(
        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,
        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,
        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,
        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,
        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND
        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        - SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_XZQH C
        WHERE A.RQ = #{req.startDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        - SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_XZQH C
        WHERE A.RQ = #{req.startDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_XZQH C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.XZQH_DM=C.XZQH_DM1
        AND A.YSKM_DM =B.YSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND TYPE_CODE = '1'
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC
        ) A ORDER BY 1,2 ASC
    </select>
    <select id="selectIncomeSeparateAreaTableBeginCityDistrict"
            resultType="com.zjhh.tax.income.vo.IncomeSeparateAreaTableVO">
        SELECT SJXZQH_DM PARENTID,YSKM_DM,YSKMMC,
        BQ_DAY, BQ_MONTH, SS_ZF, YSSAMT, BQ_YEAR,WCYS,
        SS_CE,SS_HB,
        CASE WHEN SUM(SS_CE)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE SS_CE/SUM(SS_CE)OVER(PARTITION BY
        SJXZQH_DM)*100 END ZLGXL,
        CASE WHEN SUM(BQ_YEAR)OVER(PARTITION BY SJXZQH_DM)=0 THEN NULL ELSE BQ_YEAR/SUM(BQ_YEAR)OVER(PARTITION BY
        SJXZQH_DM)*100 END QLGXL,
        CASE WHEN LENGTH(YSKM_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM(
        SELECT SJXZQH_DM,YSKM_DM,YSKMMC,
        SUM(BQ_DAY) BQ_DAY,SUM(BQ_MONTH) BQ_MONTH,
        CASE WHEN SUM(SS_MONTH)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTH)/SUM(SS_MONTH)-1)*100,2) END SS_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEAR) BQ_YEAR,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(YSSAMT))*100,2) END WCYS,
        SUM(BQ_YEAR)-SUM(SS_YEAR) SS_CE,
        CASE WHEN SUM(SS_YEAR)=0 THEN NULL ELSE ROUND((SUM(BQ_YEAR)/SUM(SS_YEAR)-1)*100,2) END SS_HB
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND
        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        SUM(A.DAYAMT) BQ_DAY,
        SUM(A.MONTHAMT) BQ_MONTH,
        SUM(A.YEARAMT) BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        0 YSSAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        SUM(MONTHAMT) SS_MONTH,
        SUM(YEARAMT) SS_YEAR,
        0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_XZQH C
        WHERE A.RQ = #{req.endDateSq}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM='000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM=C.XZQH_DM1
        AND
        <choose>
            <when test="req.ysjc != null and req.ysjc.size > 0">
                A.YSJC_DM IN
                <foreach collection="req.ysjc" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM YSKM_DM,C.XZQH_MC YSKMMC,
        0 BQ_DAY,
        0 BQ_MONTH,
        0 BQ_YEAR,
        0 SQ_MONTH,
        0 SS_MONTH,
        0 SS_YEAR,
        SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A,
        (SELECT * FROM DM_GY_YSKM_ZQ A WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND LENGTH(A.SJYSKM_DM) >= 7 ) B ,
        DM_XZQH C
        WHERE A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.XZQH_DM=C.XZQH_DM1
        AND A.YSKM_DM =B.YSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND TYPE_CODE IN ('1','2')
        GROUP BY C.SJXZQH_DM,C.XZQH_DM, C.XZQH_MC
        ) T GROUP BY SJXZQH_DM,YSKM_DM, YSKMMC
        ) A ORDER BY 1,2
    </select>
    <select id="selectYSKM" resultType="java.lang.String">
        select t.yskm_dm as code
        from dm_gy_yskm t
                 Inner join dm_gy_yskm_zq dgyz on t.yskm_dm = dgyz.yskm_dm and dgyz.sjyskm_dm = #{sjyskm}
        where t.yskm_dm NOT IN
              ('AB103060115', 'AB103060117', 'AB103060121', 'AB103060130', 'AB103060134', 'AB103060301', 'AB1030607',
               'AB103060118', 'AB103060131', 'AB103060198', 'AB103060203', 'AB103060398', 'AB103060498', 'AB1030698',
               'BB103019901')
        order by code
    </select>
</mapper>