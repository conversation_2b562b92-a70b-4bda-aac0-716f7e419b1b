<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.SeparateAreaAndSubjectMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectSeparateAreasAndSubjectProvince"
            resultType="com.zjhh.tax.statistics.vo.SeparateAreaAndSubjectIncomeVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(CASE WHEN YSKM_DM='0' THEN BQ_AMT ELSE 0 END) BQ_AMT_CZZS,
        SUM(CASE WHEN YSKM_DM='0' THEN SQ_AMT ELSE 0 END) SQ_AMT_CZZS,
        SUM(CASE WHEN YSKM_DM='0' THEN BQ_ZF ELSE 0 END) BQ_ZF_CZZS,
        SUM(CASE WHEN YSKM_DM='S' THEN BQ_AMT ELSE 0 END) BQ_AMT_SHLS,
        SUM(CASE WHEN YSKM_DM='S' THEN SQ_AMT ELSE 0 END) SQ_AMT_SHLS,
        SUM(CASE WHEN YSKM_DM='S' THEN BQ_ZF ELSE 0 END) BQ_ZF_SHLS,
        SUM(CASE WHEN YSKM_DM='SA' THEN BQ_AMT ELSE 0 END) BQ_AMT_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='SA' THEN SQ_AMT ELSE 0 END) SQ_AMT_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='SA' THEN BQ_ZF ELSE 0 END) BQ_ZF_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_AMT ELSE 0 END) BQ_AMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN SQ_AMT ELSE 0 END) SQ_AMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_ZF ELSE 0 END) BQ_ZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_AMT ELSE 0 END) BQ_AMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN SQ_AMT ELSE 0 END) SQ_AMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_ZF ELSE 0 END) BQ_ZF_SS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN BQ_AMT ELSE 0 END) BQ_AMT_ZZS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN SQ_AMT ELSE 0 END) SQ_AMT_ZZS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN BQ_ZF ELSE 0 END) BQ_ZF_ZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN BQ_AMT ELSE 0 END) BQ_AMT_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN SQ_AMT ELSE 0 END) SQ_AMT_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN BQ_ZF ELSE 0 END) BQ_ZF_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN BQ_AMT ELSE 0 END) BQ_AMT_YYS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN SQ_AMT ELSE 0 END) SQ_AMT_YYS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN BQ_ZF ELSE 0 END) BQ_ZF_YYS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN BQ_AMT ELSE 0 END) BQ_AMT_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN SQ_AMT ELSE 0 END) SQ_AMT_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN BQ_ZF ELSE 0 END) BQ_ZF_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN BQ_AMT ELSE 0 END) BQ_AMT_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN SQ_AMT ELSE 0 END) SQ_AMT_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN BQ_ZF ELSE 0 END) BQ_ZF_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN BQ_AMT ELSE 0 END) BQ_AMT_QTGS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN SQ_AMT ELSE 0 END) SQ_AMT_QTGS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN BQ_ZF ELSE 0 END) BQ_ZF_QTGS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_AMT ELSE 0 END) BQ_AMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN SQ_AMT ELSE 0 END) SQ_AMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_ZF ELSE 0 END) BQ_ZF_FSSR,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN BQ_AMT ELSE 0 END) BQ_AMT_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN SQ_AMT ELSE 0 END) SQ_AMT_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN BQ_ZF ELSE 0 END) BQ_ZF_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN BQ_AMT ELSE 0 END) BQ_AMT_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN SQ_AMT ELSE 0 END) SQ_AMT_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN BQ_ZF ELSE 0 END) BQ_ZF_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN BQ_AMT ELSE 0 END) BQ_AMT_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN SQ_AMT ELSE 0 END) SQ_AMT_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN BQ_ZF ELSE 0 END) BQ_ZF_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN BQ_AMT ELSE 0 END) BQ_AMT_GYQY,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN SQ_AMT ELSE 0 END) SQ_AMT_GYQY,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN BQ_ZF ELSE 0 END) BQ_ZF_GYQY,
        ISLEAF
        FROM (
        SELECT SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        SUM(SQ_YEARAMT) SQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(XZQH_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="req.province != null and req.province == '1'.toString()">
                        ( A.SJXZQH_DM IS NULL)
                    </when>
                    <otherwise>
                        (
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.SJXZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        OR
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.XZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        )
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        ),
        DM_YSKM AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '3') ,
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT C.SJXZQH_DM,C.XZQH_DM XZQH_DM,C.XZQH_MC XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT
        FROM ADS_TAX_INCOME_DAY A,DM_YSKM B,DM_XZQH C,DM_YSJC D
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM XZQH_DM,C.XZQH_MC XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT
        FROM ADS_TAX_INCOME_MON A,DM_YSKM B,DM_XZQH C,DM_YSJC D
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC

        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC
        ) A GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,ISLEAF
        ORDER BY 1 NULLS FIRST,2 ASC
    </select>
    <select id="selectSeparateAreasAndSubjectDistrict"
            resultType="com.zjhh.tax.statistics.vo.SeparateAreaAndSubjectIncomeVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(CASE WHEN YSKM_DM='0' THEN BQ_AMT ELSE 0 END) BQ_AMT_CZZS,
        SUM(CASE WHEN YSKM_DM='0' THEN SQ_AMT ELSE 0 END) SQ_AMT_CZZS,
        SUM(CASE WHEN YSKM_DM='0' THEN BQ_ZF ELSE 0 END) BQ_ZF_CZZS,
        SUM(CASE WHEN YSKM_DM='S' THEN BQ_AMT ELSE 0 END) BQ_AMT_SHLS,
        SUM(CASE WHEN YSKM_DM='S' THEN SQ_AMT ELSE 0 END) SQ_AMT_SHLS,
        SUM(CASE WHEN YSKM_DM='S' THEN BQ_ZF ELSE 0 END) BQ_ZF_SHLS,
        SUM(CASE WHEN YSKM_DM='SA' THEN BQ_AMT ELSE 0 END) BQ_AMT_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='SA' THEN SQ_AMT ELSE 0 END) SQ_AMT_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='SA' THEN BQ_ZF ELSE 0 END) BQ_ZF_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_AMT ELSE 0 END) BQ_AMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN SQ_AMT ELSE 0 END) SQ_AMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_ZF ELSE 0 END) BQ_ZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_AMT ELSE 0 END) BQ_AMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN SQ_AMT ELSE 0 END) SQ_AMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_ZF ELSE 0 END) BQ_ZF_SS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN BQ_AMT ELSE 0 END) BQ_AMT_ZZS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN SQ_AMT ELSE 0 END) SQ_AMT_ZZS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN BQ_ZF ELSE 0 END) BQ_ZF_ZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN BQ_AMT ELSE 0 END) BQ_AMT_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN SQ_AMT ELSE 0 END) SQ_AMT_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN BQ_ZF ELSE 0 END) BQ_ZF_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN BQ_AMT ELSE 0 END) BQ_AMT_YYS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN SQ_AMT ELSE 0 END) SQ_AMT_YYS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN BQ_ZF ELSE 0 END) BQ_ZF_YYS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN BQ_AMT ELSE 0 END) BQ_AMT_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN SQ_AMT ELSE 0 END) SQ_AMT_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN BQ_ZF ELSE 0 END) BQ_ZF_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN BQ_AMT ELSE 0 END) BQ_AMT_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN SQ_AMT ELSE 0 END) SQ_AMT_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN BQ_ZF ELSE 0 END) BQ_ZF_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN BQ_AMT ELSE 0 END) BQ_AMT_QTGS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN SQ_AMT ELSE 0 END) SQ_AMT_QTGS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN BQ_ZF ELSE 0 END) BQ_ZF_QTGS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_AMT ELSE 0 END) BQ_AMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN SQ_AMT ELSE 0 END) SQ_AMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_ZF ELSE 0 END) BQ_ZF_FSSR,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN BQ_AMT ELSE 0 END) BQ_AMT_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN SQ_AMT ELSE 0 END) SQ_AMT_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN BQ_ZF ELSE 0 END) BQ_ZF_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN BQ_AMT ELSE 0 END) BQ_AMT_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN SQ_AMT ELSE 0 END) SQ_AMT_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN BQ_ZF ELSE 0 END) BQ_ZF_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN BQ_AMT ELSE 0 END) BQ_AMT_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN SQ_AMT ELSE 0 END) SQ_AMT_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN BQ_ZF ELSE 0 END) BQ_ZF_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN BQ_AMT ELSE 0 END) BQ_AMT_GYQY,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN SQ_AMT ELSE 0 END) SQ_AMT_GYQY,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN BQ_ZF ELSE 0 END) BQ_ZF_GYQY,
        ISLEAF
        FROM (
        SELECT SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        SUM(SQ_YEARAMT) SQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(XZQH_DM)>=6 OR XZQH_DM='3300' THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM

        AND
        (
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        OR
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        )
        ),
        DM_YSKM AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '3') ,
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT C.SJXZQH_DM,C.XZQH_DM XZQH_DM,C.XZQH_MC XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_YSKM B,DM_XZQH C,DM_YSJC D
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM XZQH_DM,C.XZQH_MC XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSKM B,DM_XZQH C,DM_YSJC D
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC

        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC
        ) A GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,ISLEAF
        ORDER BY 1 NULLS FIRST,2 ASC
    </select>
    <select id="selectSeparateAreasAndSubjectCityDistrict"
            resultType="com.zjhh.tax.statistics.vo.SeparateAreaAndSubjectIncomeVO">
        SELECT SJXZQH_DM ,XZQH_DM, XZQH_MC,
        SUM(CASE WHEN YSKM_DM='0' THEN BQ_AMT ELSE 0 END) BQ_AMT_CZZS,
        SUM(CASE WHEN YSKM_DM='0' THEN SQ_AMT ELSE 0 END) SQ_AMT_CZZS,
        SUM(CASE WHEN YSKM_DM='0' THEN BQ_ZF ELSE 0 END) BQ_ZF_CZZS,
        SUM(CASE WHEN YSKM_DM='S' THEN BQ_AMT ELSE 0 END) BQ_AMT_SHLS,
        SUM(CASE WHEN YSKM_DM='S' THEN SQ_AMT ELSE 0 END) SQ_AMT_SHLS,
        SUM(CASE WHEN YSKM_DM='S' THEN BQ_ZF ELSE 0 END) BQ_ZF_SHLS,
        SUM(CASE WHEN YSKM_DM='SA' THEN BQ_AMT ELSE 0 END) BQ_AMT_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='SA' THEN SQ_AMT ELSE 0 END) SQ_AMT_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='SA' THEN BQ_ZF ELSE 0 END) BQ_ZF_SHXFZZ,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_AMT ELSE 0 END) BQ_AMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN SQ_AMT ELSE 0 END) SQ_AMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_ZF ELSE 0 END) BQ_ZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_AMT ELSE 0 END) BQ_AMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN SQ_AMT ELSE 0 END) SQ_AMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_ZF ELSE 0 END) BQ_ZF_SS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN BQ_AMT ELSE 0 END) BQ_AMT_ZZS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN SQ_AMT ELSE 0 END) SQ_AMT_ZZS,
        SUM(CASE WHEN YSKM_DM='AA10101' THEN BQ_ZF ELSE 0 END) BQ_ZF_ZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN BQ_AMT ELSE 0 END) BQ_AMT_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN SQ_AMT ELSE 0 END) SQ_AMT_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA101011' THEN BQ_ZF ELSE 0 END) BQ_ZF_QZCZZS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN BQ_AMT ELSE 0 END) BQ_AMT_YYS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN SQ_AMT ELSE 0 END) SQ_AMT_YYS,
        SUM(CASE WHEN YSKM_DM='AA10103' THEN BQ_ZF ELSE 0 END) BQ_ZF_YYS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN BQ_AMT ELSE 0 END) BQ_AMT_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN SQ_AMT ELSE 0 END) SQ_AMT_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10104' THEN BQ_ZF ELSE 0 END) BQ_ZF_QYSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN BQ_AMT ELSE 0 END) BQ_AMT_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN SQ_AMT ELSE 0 END) SQ_AMT_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10106' THEN BQ_ZF ELSE 0 END) BQ_ZF_GRSDS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN BQ_AMT ELSE 0 END) BQ_AMT_QTGS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN SQ_AMT ELSE 0 END) SQ_AMT_QTGS,
        SUM(CASE WHEN YSKM_DM='AA10199' THEN BQ_ZF ELSE 0 END) BQ_ZF_QTGS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_AMT ELSE 0 END) BQ_AMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN SQ_AMT ELSE 0 END) SQ_AMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_ZF ELSE 0 END) BQ_ZF_FSSR,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN BQ_AMT ELSE 0 END) BQ_AMT_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN SQ_AMT ELSE 0 END) SQ_AMT_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB1030203' THEN BQ_ZF ELSE 0 END) BQ_ZF_JYFFJ,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN BQ_AMT ELSE 0 END) BQ_AMT_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN SQ_AMT ELSE 0 END) SQ_AMT_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10304' THEN BQ_ZF ELSE 0 END) BQ_ZF_XZSY,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN BQ_AMT ELSE 0 END) BQ_AMT_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN SQ_AMT ELSE 0 END) SQ_AMT_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10305' THEN BQ_ZF ELSE 0 END) BQ_ZF_FMSR,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN BQ_AMT ELSE 0 END) BQ_AMT_GYQY,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN SQ_AMT ELSE 0 END) SQ_AMT_GYQY,
        SUM(CASE WHEN YSKM_DM='AB10306' THEN BQ_ZF ELSE 0 END) BQ_ZF_GYQY,
        ISLEAF
        FROM (
        SELECT SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        SUM(SQ_YEARAMT) SQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(XZQH_DM)>=6 OR XZQH_DM='3300' THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND
        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        DM_YSKM AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '3') ,
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT C.SJXZQH_DM,C.XZQH_DM XZQH_DM,C.XZQH_MC XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_YSKM B,DM_XZQH C,DM_YSJC D
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM XZQH_DM,C.XZQH_MC XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSKM B,DM_XZQH C,DM_YSJC D
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC

        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC
        ) A GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,ISLEAF
        ORDER BY XZQH_DM NULLS FIRST,2 ASC
    </select>
</mapper>