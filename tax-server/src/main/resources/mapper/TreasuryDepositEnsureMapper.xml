<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.special.dao.mapper.TreasuryDepositEnsureMapper">

    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>

    <select id="selectTreasuryDepositEnsureNotZero" resultType="com.zjhh.tax.special.vo.TreasuryDepositEnsureVO">
        SELECT A.*,
        CASE WHEN BZSP >= 1 THEN 1 WHEN BZSP &lt;= 0.3 AND BZSP > 0.1 THEN 2 WHEN BZSP &lt;= 0.1 THEN 3 ELSE 0 END AS
        TYPE,
        CASE WHEN BZSP >= 1 THEN '紫色' WHEN BZSP &lt;= 0.3 AND BZSP > 0.1 THEN '黄色' WHEN BZSP &lt;= 0.1 THEN '红色'
        ELSE
        '白色' END AS TYPE_NAME
        FROM (
        SELECT B.XH,B.ZS_DM XZQH_DM,
        <if test="req.roleType == '1'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType == '2'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            B.ZS_MC
        </if>
        XZQH_MC,
        SUM(BQ_KKYE) AS BQ_KKYE,SUM(SYM_KKYE) AS SYM_KKYE,SUM(SQ_KKYE) AS SQ_KKYE,
        CASE WHEN TO_NUMBER(#{req.mon}) = 0 THEN 0 ELSE SUM(LJLC)/ TO_NUMBER(#{req.mon}) END AS LJLC ,
        CASE WHEN SUM(LJLC)=0 THEN 0 ELSE ROUND(SUM(BQ_KKYE)/SUM(LJLC)* TO_NUMBER(#{req.mon}),2) END AS BZSP
        FROM (
        SELECT T.XZQH_DM,T.XZQH_MC,
        SUM(TODAYBALANCE) AS BQ_KKYE,0 AS SYM_KKYE,0 AS SQ_KKYE,0 AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ = #{req.endDate}
        AND T.KKLX_DM = '1'
        GROUP BY T.XZQH_DM,T.XZQH_MC
        UNION ALL
        SELECT T.XZQH_DM,T.XZQH_MC,
        0 AS BQ_KKYE,SUM(TODAYBALANCE) AS SYM_KKYE,0 AS SQ_KKYE,0 AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ = #{req.sydate}
        AND T.KKLX_DM = '1'

        GROUP BY T.XZQH_DM,T.XZQH_MC
        UNION ALL
        SELECT T.XZQH_DM,T.XZQH_MC,
        0 AS BQ_KKYE,0 AS SYM_KKYE,SUM(TODAYBALANCE) AS SQ_KKYE,0 AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMMDD')
        AND T.KKLX_DM = '1'
        GROUP BY T.XZQH_DM,T.XZQH_MC
        UNION ALL
        SELECT T.XZQH_DM,T.XZQH_MC,
        0 AS BQ_KKYE, 0 AS SYM_KKYE,0 AS SQ_KKYE,SUM(T.TODAYPAY) AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ BETWEEN SUBSTR(#{req.endDate},1,4)||'0101' and #{req.sydate1}
        AND T.KKLX_DM = '1'
        GROUP BY T.XZQH_DM,T.XZQH_MC
        ) T ,DM_GY_PAGE_STYLE B
        WHERE T.XZQH_DM = B.MID_DM
        AND B.TYPE_DM = '5'

        <if test="req.roleType == '1'.toString()">
            AND 1=1
        </if>
        <if test="req.roleType == '2'.toString()">
            <choose>
                <when test="req.qxgl == '330482'">
                    AND T.XZQH_DM in ('330482','330485')
                </when>
                <otherwise>
                    AND SUBSTR(T.XZQH_DM,1,4) = #{req.qxgl}
                </otherwise>
            </choose>

            AND B.DMMX IN ('3','4')
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            <choose>
                <when test="req.qxgl == '330482'">
                    AND T.XZQH_DM in ('330482','330485')
                </when>
                <otherwise>
                    AND T.XZQH_DM = #{req.qxgl}
                </otherwise>
            </choose>

            AND B.DMMX = '4'
        </if>
        <choose>
            <when test="req.qxgl.length == 4">
                <choose>
                    <when test="req.qxgl == '3300'.toString()">
                        AND 1=1
                    </when>
                    <otherwise>
                        <choose>
                            <when test="req.qxgl == '330482'">
                                AND T.XZQH_DM in ('330482','330485')
                            </when>
                            <otherwise>
                                AND SUBSTR(T.XZQH_DM,1,4) = #{req.qxgl}
                            </otherwise>
                        </choose>

                        AND B.DMMX IN ('3','4')
                    </otherwise>
                </choose>
            </when>

        </choose>
        GROUP BY B.XH,B.ZS_DM,
        <if test="req.roleType == '1'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType == '2'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            B.ZS_MC
        </if>
        ) A
        ORDER BY A.XH
    </select>
    <select id="selectTreasuryDepositEnsure" resultType="com.zjhh.tax.special.vo.TreasuryDepositEnsureVO">
        SELECT A.*,
        CASE WHEN BZSP >= 1 THEN 1 WHEN BZSP &lt;= 0.3 AND BZSP > 0.1 THEN 2 WHEN BZSP &lt;= 0.1 THEN 3 ELSE 0 END AS
        TYPE,
        CASE WHEN BZSP >= 1 THEN '紫色' WHEN BZSP &lt;= 0.3 AND BZSP > 0.1 THEN '黄色' WHEN BZSP &lt;= 0.1 THEN '红色'
        ELSE
        '白色' END AS TYPE_NAME
        FROM (
        SELECT B.XH,B.ZS_DM XZQH_DM,
        <if test="req.roleType == '1'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType == '2'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            B.ZS_MC
        </if>
        XZQH_MC,
        SUM(BQ_KKYE) AS BQ_KKYE,SUM(SYM_KKYE) AS SYM_KKYE,SUM(SQ_KKYE) AS SQ_KKYE,
        SUM(LJLC) * cast(#{req.workDate} as NUMERIC) / cast(#{req.allDate} as numeric) AS LJLC ,
        CASE WHEN SUM(LJLC) = 0 THEN 0 ELSE CASE WHEN cast(#{req.allDate} as numeric) = 0 THEN
        ROUND(SUM(BQ_KKYE)/SUM(LJLC),2) ELSE ROUND(SUM(BQ_KKYE)/(SUM(LJLC) * cast(#{req.workDate} as
        numeric)/cast(#{req.allDate} as numeric)),2) END END AS BZSP
        FROM (
        SELECT T.XZQH_DM,T.XZQH_MC,
        SUM(TODAYBALANCE) AS BQ_KKYE,0 AS SYM_KKYE,0 AS SQ_KKYE,0 AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ = #{req.endDate}
        AND T.KKLX_DM = '1'
        GROUP BY T.XZQH_DM,T.XZQH_MC
        UNION ALL
        SELECT T.XZQH_DM,T.XZQH_MC,
        0 AS BQ_KKYE,SUM(TODAYBALANCE) AS SYM_KKYE,0 AS SQ_KKYE,0 AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ = #{req.sydate}
        AND T.KKLX_DM = '1'

        GROUP BY T.XZQH_DM,T.XZQH_MC
        UNION ALL
        SELECT T.XZQH_DM,T.XZQH_MC,
        0 AS BQ_KKYE,0 AS SYM_KKYE,SUM(TODAYBALANCE) AS SQ_KKYE,0 AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMMDD')
        AND T.KKLX_DM = '1'
        GROUP BY T.XZQH_DM,T.XZQH_MC
        UNION ALL
        SELECT T.XZQH_DM,T.XZQH_MC,
        0 AS BQ_KKYE, 0 AS SYM_KKYE,0 AS SQ_KKYE,SUM(T.TODAYPAY) AS LJLC
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ BETWEEN SUBSTR(#{req.endDate},1,4)||'0101' AND #{req.endDate}
        AND T.KKLX_DM = '1'
        GROUP BY T.XZQH_DM,T.XZQH_MC
        ) T ,DM_GY_PAGE_STYLE B
        WHERE T.XZQH_DM = B.MID_DM
        AND B.TYPE_DM = '5'
        AND
        <if test="req.roleType == '1'.toString()">
            1=1
        </if>
        <if test="req.roleType == '2'.toString()">
            <choose>
                <when test="req.qxgl == '330482'">
                    T.XZQH_DM in ('330482','330485')
                </when>
                <otherwise>
                    SUBSTR(T.XZQH_DM,1,4) = #{req.qxgl}
                </otherwise>
            </choose>
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            <choose>
                <when test="req.qxgl == '330482'">
                    T.XZQH_DM in ('330482','330485')
                </when>
                <otherwise>
                    T.XZQH_DM = #{req.qxgl}
                </otherwise>
            </choose>

        </if>
        <choose>
            <when test="req.qxgl.length == 4">
                <choose>
                    <when test="req.qxgl == '3300'.toString()">
                        AND 1=1
                    </when>
                    <otherwise>
                        <choose>
                            <when test="req.qxgl == '330482'">
                                AND T.XZQH_DM in ('330482','330485')
                            </when>
                            <otherwise>
                                AND SUBSTR(T.XZQH_DM,1,4) = #{req.qxgl}
                            </otherwise>
                        </choose>
                    </otherwise>
                </choose>
            </when>

        </choose>
        AND
        <if test="req.roleType == '1'.toString()">
            1=1
        </if>
        <if test="req.roleType == '2'.toString()">
            B.DMMX IN ('3','4')
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            B.DMMX = '4'
        </if>
        <choose>
            <when test="req.qxgl.length == 4">
                <choose>
                    <when test="req.qxgl == '3300'.toString()">
                        AND 1=1
                    </when>
                    <otherwise>
                        AND B.DMMX IN ('3','4')
                    </otherwise>
                </choose>
            </when>

        </choose>

        GROUP BY B.XH,B.ZS_DM,
        <if test="req.roleType == '1'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType == '2'.toString()">
            CONCAT(B.BZ,B.ZS_MC)
        </if>
        <if test="req.roleType != '1'.toString() and req.roleType != '2'.toString()">
            B.ZS_MC
        </if>
        ) A
        ORDER BY A.XH
    </select>
    <select id="selectTreasuryDepositChangeByYear" resultType="com.zjhh.tax.special.vo.TreasuryDepositChangeChartVO">
        SELECT XZQH_DM,XZQHMC as xzqhMc,RQ BBQ,KKYE,CASE WHEN LENGTH(XZQH_DM)=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DATA1 AS (
        SELECT T.RQ,T.XZQH_DM,T.TODAYBALANCE
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        AND T.KKLX_DM = '1'
        )
        SELECT C.SJXZQH_DM XZQH_DM,
        C.SJXZQH_MC XZQHMC,
        SUBSTR(A.RQ,1,4) RQ,
        SUM(A.TODAYBALANCE) KKYE
        FROM DATA1 A,VW_DM_KK_XZQH_ZQ C,
        (SELECT T.XZQH_DM,SUBSTR(T.RQ,1,4),MAX(T.RQ) DATE1 FROM DATA1 T GROUP BY T.XZQH_DM,SUBSTR(T.RQ,1,4)) D
        WHERE A.RQ = D.DATE1
        AND A.XZQH_DM = D.XZQH_DM
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                C.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJXZQH_DM IN('3303','3304','3305','3306','3307','3308','3309','3310','3311')
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.SJXZQH_DM, C.SJXZQH_MC,SUBSTR(A.RQ,1,4)
        ) A ORDER BY 1 ASC
    </select>
    <select id="selectTreasuryDepositChangeByMonth" resultType="com.zjhh.tax.special.vo.TreasuryDepositChangeChartVO">
        SELECT XZQH_DM,XZQHMC,RQ BBQ,KKYE,CASE WHEN LENGTH(XZQH_DM)=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DATA1 AS (
        SELECT T.RQ,T.XZQH_DM,T.TODAYBALANCE
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        AND T.KKLX_DM = '1'

        )
        SELECT C.SJXZQH_DM XZQH_DM,
        C.SJXZQH_MC XZQHMC,
        SUBSTR(A.RQ,1,6) RQ,
        SUM(A.TODAYBALANCE) KKYE
        FROM DATA1 A,VW_DM_KK_XZQH_ZQ C,
        (SELECT T.XZQH_DM,SUBSTR(T.RQ,1,6),MAX(T.RQ) DATE1 FROM DATA1 T GROUP BY T.XZQH_DM,SUBSTR(T.RQ,1,6)) D
        WHERE A.RQ = D.DATE1
        AND A.XZQH_DM = D.XZQH_DM
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                C.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJXZQH_DM IN('3303','3304','3305','3306','3307','3308','3309','3310','3311')
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.SJXZQH_DM, C.SJXZQH_MC,SUBSTR(A.RQ,1,6)
        ) A ORDER BY 1 ASC
    </select>
    <select id="selectTreasuryDepositChangeByDay" resultType="com.zjhh.tax.special.vo.TreasuryDepositChangeChartVO">
        SELECT XZQH_DM,XZQHMC,RQ BBQ,KKYE,CASE WHEN LENGTH(XZQH_DM)=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DATA1 AS (
        SELECT T.RQ,T.XZQH_DM,T.TODAYBALANCE
        FROM ADS_TAX_STOCK_DAY T
        WHERE T.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        AND T.KKLX_DM = '1'
        )
        SELECT C.SJXZQH_DM XZQH_DM,
        C.SJXZQH_MC XZQHMC,
        A.RQ RQ,
        SUM(A.TODAYBALANCE) KKYE
        FROM DATA1 A,VW_DM_KK_XZQH_ZQ C
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                C.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJXZQH_DM IN('3303','3304','3305','3306','3307','3308','3309','3310','3311')
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM
        GROUP BY C.SJXZQH_DM, C.SJXZQH_MC,A.RQ
        ) A ORDER BY 1 ASC
    </select>
    <select id="selectTreasuryDepositChangeTableByYear"
            resultType="com.zjhh.tax.special.vo.TreasuryDepositChangeTableVO">
        SELECT PARENTID,XZQH_DM,XZQHMC,RQ BBQ,
        SUM(ZKKYE) ZKKYE,SUM(KKYE) KKYE,SUM(SBYE) SBYE, ISLEAF
        FROM (
        WITH DATA1 AS (
        SELECT T.RQ,T.XZQH_DM,
        SUM(T.ZKKYE) AS ZKKYE ,SUM(T.KKYE) AS KKYE,SUM(T.SBYE) AS SBYE
        FROM (
        SELECT T.RQ,T.XZQH_DM,
        CASE WHEN T.KKLX_DM IN ('1','2') THEN T.TODAYBALANCE ELSE 0 END AS ZKKYE,-- 总库款余额
        CASE WHEN T.KKLX_DM = '1' THEN T.TODAYBALANCE ELSE 0 END AS KKYE, -- 库款余额
        CASE WHEN T.KKLX_DM = '2' THEN T.TODAYBALANCE ELSE 0 END AS SBYE -- 社保余额
        FROM ADS_TAX_STOCK_DAY T
        WHERE RQ BETWEEN #{req.startDate} AND #{req.endDate}
        ) T GROUP BY T.RQ,T.XZQH_DM
        )
        SELECT C.SJXZQH_DM PARENTID,C.XZQH_DM,C.XZQH_MC XZQHMC,
        SUBSTR(A.RQ,1,4) RQ,C.ISLEAF,
        ZKKYE ,KKYE,SBYE
        FROM DATA1 A,
        (SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,A.ISLEAF,B.XZQH_DM XZQH_DM1
        FROM VW_DM_KK_XZQH A, VW_DM_KK_XZQH_ZQ B
        WHERE
        <if test="(req.xzqh == null or req.xzqh.size == 0) and req.province == '1'.toString()">
            A.xzqh_dm = '3300'
        </if>
        <if test="(req.xzqh == null or req.xzqh.size == 0) and req.province != '1'.toString()">
            A.XZQH_DM IN
            <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.xzqh != null and req.xzqh.size > 0">
            A.SJXZQH_DM IN
            <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND A.XZQH_DM = B.SJXZQH_DM
        ) C,
        (SELECT T.XZQH_DM,SUBSTR(RQ,1,4),MAX(T.RQ) DATE1 FROM DATA1 T
        WHERE T.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        GROUP BY T.XZQH_DM,SUBSTR(RQ,1,4)
        ) D
        WHERE A.RQ = D.DATE1
        AND A.XZQH_DM = D.XZQH_DM
        AND A.XZQH_DM = C.XZQH_DM1
        ) A
        GROUP BY PARENTID,XZQH_DM,XZQHMC,RQ,ISLEAF
        ORDER BY 2

    </select>
    <select id="selectTreasuryDepositChangeTableByMonth"
            resultType="com.zjhh.tax.special.vo.TreasuryDepositChangeTableVO">
        SELECT PARENTID,XZQH_DM,XZQHMC,RQ BBQ,
        SUM(ZKKYE) ZKKYE,SUM(KKYE) KKYE,SUM(SBYE) SBYE, ISLEAF
        FROM (
        WITH DATA1 AS (
        SELECT T.RQ,T.XZQH_DM,
        SUM(T.ZKKYE) AS ZKKYE ,SUM(T.KKYE) AS KKYE,SUM(T.SBYE) AS SBYE
        FROM (
        SELECT T.RQ,T.XZQH_DM,
        CASE WHEN T.KKLX_DM IN ('1','2') THEN T.TODAYBALANCE ELSE 0 END AS ZKKYE,-- 总库款余额
        CASE WHEN T.KKLX_DM = '1' THEN T.TODAYBALANCE ELSE 0 END AS KKYE, -- 库款余额
        CASE WHEN T.KKLX_DM = '2' THEN T.TODAYBALANCE ELSE 0 END AS SBYE -- 社保余额
        FROM ADS_TAX_STOCK_DAY T
        WHERE RQ BETWEEN #{req.startDate} AND #{req.endDate}
        ) T GROUP BY T.RQ,T.XZQH_DM
        )
        SELECT C.SJXZQH_DM PARENTID,C.XZQH_DM,C.XZQH_MC XZQHMC,
        SUBSTR(A.RQ,1,6) RQ,C.ISLEAF,
        ZKKYE ,KKYE,SBYE
        FROM DATA1 A,
        (SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,A.ISLEAF,B.XZQH_DM XZQH_DM1
        FROM VW_DM_KK_XZQH A, VW_DM_KK_XZQH_ZQ B
        WHERE
        <if test="(req.xzqh == null or req.xzqh.size == 0)and req.province == '1'.toString()">
            A.xzqh_dm = '3300'
        </if>
        <if test="(req.xzqh == null or req.xzqh.size == 0) and req.province != '1'.toString()">
            A.XZQH_DM IN
            <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.xzqh != null and req.xzqh.size > 0">
            A.SJXZQH_DM IN
            <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND A.XZQH_DM = B.SJXZQH_DM
        ) C,
        (SELECT T.XZQH_DM,SUBSTR(RQ,1,6),MAX(T.RQ) DATE1 FROM DATA1 T
        WHERE T.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        GROUP BY T.XZQH_DM,SUBSTR(RQ,1,6)
        ) D
        WHERE A.RQ = D.DATE1
        AND A.XZQH_DM = D.XZQH_DM
        AND A.XZQH_DM = C.XZQH_DM1
        ) A
        GROUP BY PARENTID,XZQH_DM,XZQHMC,RQ,ISLEAF
        ORDER BY 2
    </select>
    <select id="selectTreasuryDepositChangeTableByDay"
            resultType="com.zjhh.tax.special.vo.TreasuryDepositChangeTableVO">
        SELECT PARENTID,XZQH_DM,XZQHMC,RQ BBQ,
        SUM(ZKKYE) ZKKYE,SUM(KKYE) KKYE,SUM(SBYE) SBYE, ISLEAF
        FROM (
        WITH DATA1 AS (
        SELECT T.RQ,T.XZQH_DM,
        SUM(T.ZKKYE) AS ZKKYE ,SUM(T.KKYE) AS KKYE,SUM(T.SBYE) AS SBYE
        FROM (
        SELECT T.RQ,T.XZQH_DM,
        CASE WHEN T.KKLX_DM IN ('1','2') THEN T.TODAYBALANCE ELSE 0 END AS ZKKYE,-- 总库款余额
        CASE WHEN T.KKLX_DM = '1' THEN T.TODAYBALANCE ELSE 0 END AS KKYE, -- 库款余额
        CASE WHEN T.KKLX_DM = '2' THEN T.TODAYBALANCE ELSE 0 END AS SBYE -- 社保余额
        FROM ADS_TAX_STOCK_DAY T
        WHERE RQ BETWEEN #{req.startDate} AND #{req.endDate}
        ) T GROUP BY T.RQ,T.XZQH_DM
        )
        SELECT C.SJXZQH_DM PARENTID,C.XZQH_DM,C.XZQH_MC XZQHMC,
        A.RQ RQ,C.ISLEAF,
        ZKKYE ,KKYE,SBYE
        FROM DATA1 A,
        (SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,A.ISLEAF,B.XZQH_DM XZQH_DM1
        FROM VW_DM_KK_XZQH A, VW_DM_KK_XZQH_ZQ B
        WHERE
        <if test="(req.xzqh == null or req.xzqh.size == 0) and req.province == '1'.toString()">
            A.xzqh_dm = '3300'
        </if>
        <if test="(req.xzqh == null or req.xzqh.size == 0) and req.province != '1'.toString()">
            A.XZQH_DM IN
            <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.xzqh != null and req.xzqh.size > 0">
            A.SJXZQH_DM IN
            <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND A.XZQH_DM = B.SJXZQH_DM
        ) C
        WHERE A.XZQH_DM = C.XZQH_DM1
        ) A
        GROUP BY PARENTID,XZQH_DM,XZQHMC,RQ,ISLEAF
        ORDER BY 2
    </select>
</mapper>