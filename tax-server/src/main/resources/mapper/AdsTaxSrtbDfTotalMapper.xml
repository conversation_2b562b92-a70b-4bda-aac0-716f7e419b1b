<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.tax.datamanagement.dao.mapper.AdsTaxSrtbDfTotalMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , srtb_df_id, yb_amt, yb_zf, ss_amt, ss_zf, fs_amt, fs_zf
    </sql>
    <select id="listTotal" resultType="com.zjhh.tax.datamanagement.vo.sepmonth.TotalVo">
        select t1.id,
               t1.srtb_df_id,
               t1.xzqh_dm,
               t3.xzqh_mc,
               t1.yb_amt,
               t1.yb_zf,
               t1.ss_amt,
               t1.ss_zf,
               t1.fs_amt,
               t1.fs_zf
        from ads_tax_srtb_df_total t1
                 left join ads_tax_srtb_df t2 on t1.srtb_df_id = t2.id
                 left join dm_cz_xzqh t3 on t1.xzqh_dm = t3.xzqh_dm
        where t1.srtb_df_id = #{srtbDfId}
        order by t1.xzqh_dm
    </select>

</mapper>
