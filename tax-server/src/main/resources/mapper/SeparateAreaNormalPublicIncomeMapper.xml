<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.SeparateAreaNormalPublicIncomeMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectSeparateAreaNormalPublicIncomeProvince"
            resultType="com.zjhh.tax.statistics.vo.SeparateAreaNormalPublicIncomeVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_SS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_FSSR,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_SS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_FSSR,
        ISLEAF
        FROM (
        SELECT SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(BQ_YEARAMT) BQ_YEARAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END BQ_MONTHZF,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_YEARZF,
        CASE WHEN LENGTH(XZQH_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="req.province != null and req.province == '1'.toString()">
                        ( A.SJXZQH_DM IS NULL)
                    </when>
                    <otherwise>
                        (
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.SJXZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        OR
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.XZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        )
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        ),
        DM_YSKM AS (
        SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '3'
        ),
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT,SUM(A.YEARAMT) BQ_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT
        FROM ADS_TAX_INCOME_DAY A,DM_YSKM B, DM_XZQH C,DM_YSJC D
        WHERE A.RQ = #{req.endDate}
        AND A.ZSJG_DM='000000000000'
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT,SUM(A.YEARAMT) SQ_YEARAMT
        FROM ADS_TAX_INCOME_MON A,DM_YSKM B, DM_XZQH C,DM_YSJC D
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND A.ZSJG_DM='000000000000'
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC
        ) A GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,ISLEAF
        ORDER BY 2 ASC

    </select>
    <select id="selectSeparateAreaNormalPublicIncomeDistrict"
            resultType="com.zjhh.tax.statistics.vo.SeparateAreaNormalPublicIncomeVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_SS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_FSSR,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_SS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_FSSR,
        ISLEAF
        FROM (
        SELECT SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(BQ_YEARAMT) BQ_YEARAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END BQ_MONTHZF,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_YEARZF,
        CASE WHEN LENGTH(XZQH_DM)>=6 OR XZQH_DM='3300' THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND
        (
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        OR
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        )
        ),
        DM_YSKM AS (
        SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '3'
        ),
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT,SUM(A.YEARAMT) BQ_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_YSKM B, DM_XZQH C,DM_YSJC D
        WHERE A.RQ = #{req.endDate}
        AND A.ZSJG_DM='000000000000'
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT,SUM(A.YEARAMT) SQ_YEARAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSKM B, DM_XZQH C,DM_YSJC D
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND A.ZSJG_DM='000000000000'
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC
        ) A GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,ISLEAF
        ORDER BY 2 ASC
    </select>
    <select id="selectSeparateAreaNormalPublicIncomeCityDistrict"
            resultType="com.zjhh.tax.statistics.vo.SeparateAreaNormalPublicIncomeVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_SS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_MONTHAMT ELSE 0 END) BQ_MONTHAMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_MONTHZF ELSE 0 END) BQ_MONTHZF_FSSR,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_YBYS,
        SUM(CASE WHEN YSKM_DM='A' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_YBYS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_SS,
        SUM(CASE WHEN YSKM_DM='AA101' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_SS,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_YEARAMT ELSE 0 END) BQ_YEARAMT_FSSR,
        SUM(CASE WHEN YSKM_DM='AB103' THEN BQ_YEARZF ELSE 0 END) BQ_YEARZF_FSSR,
        ISLEAF
        FROM (
        SELECT SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(BQ_YEARAMT) BQ_YEARAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END BQ_MONTHZF,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_YEARZF,
        CASE WHEN LENGTH(XZQH_DM)>=6 OR XZQH_DM='3300' THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND
        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        DM_YSKM AS (
        SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '3'
        ),
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT,SUM(A.YEARAMT) BQ_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_YSKM B, DM_XZQH C,DM_YSJC D
        WHERE A.RQ = #{req.endDate}
        AND A.ZSJG_DM='000000000000'
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        B.ZS_DM YSKM_DM,B.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT,SUM(A.YEARAMT) SQ_YEARAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSKM B, DM_XZQH C,DM_YSJC D
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND A.ZSJG_DM='000000000000'
        AND A.YSKM_DM = B.DMMX
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = C.XZQH_DM1
        AND A.XZQH_DM = D.MID_DM
        AND INSTR(D.DMMX,A.YSJC_DM) > 0
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,B.ZS_DM,B.ZS_MC
        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,YSKM_DM,YSKM_MC
        ) A GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC,ISLEAF
        ORDER BY 2 ASC
    </select>
</mapper>