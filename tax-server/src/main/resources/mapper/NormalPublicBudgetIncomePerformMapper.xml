<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.NormalPublicBudgetIncomePerformMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectNormalPublicBudgetIncomePerformProvince"
            resultType="com.zjhh.tax.statistics.vo.NormalPublicBudgetIncomePerformVO">
        SELECT YSKM_DM,YSKM_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        CASE WHEN SUM(SY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SY_MONTHAMT)-1)*100,2) END SY_ZF,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END SQ_ZF,
        SUM(YSSAMT) YSSAMT,
        SUM(BQ_YEARAMT) BQ_YEARAMT,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND(SUM(BQ_YEARAMT)/SUM(YSSAMT)*100,2) END WCJD,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END SN_ZF,
        CASE WHEN SUM(SSY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(SY_MONTHAMT)/SUM(SSY_MONTHAMT)-1)*100,2) END SSY_ZF,
        CASE WHEN SUM(SSQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(SQ_YEARAMT)/SUM(SSQ_YEARAMT)-1)*100,2) END SSQ_ZF,
        SUM(SY_MONTHAMT) SY_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(SSY_YEARAMT) SSY_MONTHAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        SUM(SY_YEARAMT) SY_YEARAMT,
        SUM(BQ_MONTHAMT)-SUM(SQ_MONTHAMT) SQ_MONTH_CE,
        SUM(BQ_YEARAMT)-SUM(SQ_YEARAMT) SQ_YEAR_CE,
        SUM(BQ_MONTHAMT)-SUM(SY_MONTHAMT) SY_MONTH_CE,
        SEQ
        FROM (
        WITH DM_YSKM AS (
        SELECT A.*,B.YSKM_DM AS CODE
        FROM DM_GY_PAGE_STYLE A,DM_GY_YSKM_ZQ B
        WHERE A.TYPE_DM = '1'
        AND A.DMMX = B.SJYSKM_DM
        )
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT,SUM(A.YEARAMT) BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_DAY A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SY_MONTHAMT,SUM(A.YEARAMT) SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-1),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        SUM(A.MONTHAMT) SSY_MONTHAMT,SUM(A.YEARAMT) SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-13),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        SUM(A.YEARAMT) SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-24),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,SUM(A.YSSAMT) YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_YSS_YEAR A LEFT JOIN
        (SELECT DISTINCT ZS_DM,ZS_MC,XH FROM DM_YSKM ) C
        ON A.SJYSKM_DM = C.ZS_DM
        WHERE C.ZS_DM IS NOT NULL
        AND A.RQ = SUBSTR(#{req.endDate},1,4)
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.XZQH_DM='3300'
            </otherwise>
        </choose>
        AND TYPE_CODE = '0'
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        ) T
        WHERE CASE WHEN SUBSTR(#{req.endDate},1,4) >= '2019' THEN YSKM_DM &lt;> 'SD' ELSE 1=1 END
        GROUP BY YSKM_DM,YSKM_MC,SEQ
        ORDER BY SEQ ASC
    </select>
    <select id="selectNormalPublicBudgetIncomePerformDistrict"
            resultType="com.zjhh.tax.statistics.vo.NormalPublicBudgetIncomePerformVO">
        SELECT YSKM_DM,YSKM_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        CASE WHEN SUM(SY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SY_MONTHAMT)-1)*100,2) END SY_ZF,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END SQ_ZF,
        SUM(YSSAMT) YSSAMT,
        SUM(BQ_YEARAMT) BQ_YEARAMT,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND(SUM(BQ_YEARAMT)/SUM(YSSAMT)*100,2) END WCJD,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END SN_ZF,
        CASE WHEN SUM(SSY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(SY_MONTHAMT)/SUM(SSY_MONTHAMT)-1)*100,2) END SSY_ZF,
        CASE WHEN SUM(SSQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(SQ_YEARAMT)/SUM(SSQ_YEARAMT)-1)*100,2) END SSQ_ZF,
        SUM(SY_MONTHAMT) SY_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(SSY_YEARAMT) SSY_MONTHAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        SUM(SY_YEARAMT) SY_YEARAMT,
        SUM(BQ_MONTHAMT)-SUM(SQ_MONTHAMT) SQ_MONTH_CE,
        SUM(BQ_YEARAMT)-SUM(SQ_YEARAMT) SQ_YEAR_CE,
        SUM(BQ_MONTHAMT)-SUM(SY_MONTHAMT) SY_MONTH_CE,
        SEQ
        FROM (
        WITH DM_YSKM AS (
        SELECT A.*,B.YSKM_DM AS CODE
        FROM DM_GY_PAGE_STYLE A,DM_GY_YSKM_ZQ B
        WHERE A.TYPE_DM = '1'
        AND A.DMMX = B.SJYSKM_DM
        )
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT,SUM(A.YEARAMT) BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_QX_DAY A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SY_MONTHAMT,SUM(A.YEARAMT) SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_QX_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-1),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_QX_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        SUM(A.MONTHAMT) SSY_MONTHAMT,SUM(A.YEARAMT) SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_QX_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-13),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        SUM(A.YEARAMT) SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_QX_MON A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-24),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,SUM(A.YSSAMT) YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_YSS_YEAR A LEFT JOIN
        (SELECT DISTINCT ZS_DM,ZS_MC,XH FROM DM_YSKM ) C
        ON A.SJYSKM_DM = C.ZS_DM
        WHERE C.ZS_DM IS NOT NULL
        AND A.RQ = SUBSTR(#{req.endDate},1,4)
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.XZQH_DM='3300'
            </otherwise>
        </choose>
        AND TYPE_CODE = '1'
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        ) T
        WHERE CASE WHEN SUBSTR(#{req.endDate},1,4) >= '2019' THEN YSKM_DM &lt;> 'SD' ELSE 1=1 END
        GROUP BY YSKM_DM,YSKM_MC,SEQ
        ORDER BY SEQ ASC
    </select>
    <select id="selectNormalPublicBudgetIncomePerformCityDistrict"
            resultType="com.zjhh.tax.statistics.vo.NormalPublicBudgetIncomePerformVO">
        SELECT YSKM_DM,YSKM_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        CASE WHEN SUM(SY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SY_MONTHAMT)-1)*100,2) END SY_ZF,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END SQ_ZF,
        SUM(YSSAMT) YSSAMT,
        SUM(BQ_YEARAMT) BQ_YEARAMT,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND(SUM(BQ_YEARAMT)/SUM(YSSAMT)*100,2) END WCJD,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END SN_ZF,
        CASE WHEN SUM(SSY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(SY_MONTHAMT)/SUM(SSY_MONTHAMT)-1)*100,2) END SSY_ZF,
        CASE WHEN SUM(SSQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(SQ_YEARAMT)/SUM(SSQ_YEARAMT)-1)*100,2) END SSQ_ZF,
        SUM(SY_MONTHAMT) SY_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(SSY_YEARAMT) SSY_MONTHAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        SUM(SY_YEARAMT) SY_YEARAMT,
        SUM(BQ_MONTHAMT)-SUM(SQ_MONTHAMT) SQ_MONTH_CE,
        SUM(BQ_YEARAMT)-SUM(SQ_YEARAMT) SQ_YEAR_CE,
        SUM(BQ_MONTHAMT)-SUM(SY_MONTHAMT) SY_MONTH_CE,
        SEQ
        FROM (
        WITH DM_YSKM AS (
        SELECT A.*,B.YSKM_DM AS CODE
        FROM DM_GY_PAGE_STYLE A,DM_GY_YSKM_ZQ B
        WHERE A.TYPE_DM = '1'
        AND A.DMMX = B.SJYSKM_DM
        )
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT,SUM(A.YEARAMT) BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM VW_ADS_TAX_INCOME_DAY_ALL A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SY_MONTHAMT,SUM(A.YEARAMT) SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM VW_ADS_TAX_INCOME_MON_ALL A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-1),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM VW_ADS_TAX_INCOME_MON_ALL A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        SUM(A.MONTHAMT) SSY_MONTHAMT,SUM(A.YEARAMT) SSY_YEARAMT,
        0 SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM VW_ADS_TAX_INCOME_MON_ALL A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-13),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        SUM(A.YEARAMT) SSQ_YEARAMT,0 YSSAMT,
        C.XH SEQ
        FROM VW_ADS_TAX_INCOME_MON_ALL A
        LEFT JOIN (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B ON A.XZQH_DM = B.MID_DM
        LEFT JOIN DM_YSKM C ON A.YSKM_DM =C.CODE
        WHERE B.MID_DM IS NOT NULL
        AND C.CODE IS NOT NULL
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-24),'YYYYMM')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        UNION ALL
        SELECT C.ZS_DM YSKM_DM, C.ZS_MC YSKM_MC,
        0 BQ_MONTHAMT,0 BQ_YEARAMT,
        0 SY_MONTHAMT,0 SY_YEARAMT,
        0 SQ_MONTHAMT,0 SQ_YEARAMT,
        0 SSY_MONTHAMT,0 SSY_YEARAMT,
        0 SSQ_YEARAMT,SUM(A.YSSAMT) YSSAMT,
        C.XH SEQ
        FROM ADS_TAX_INCOME_YSS_YEAR A LEFT JOIN
        (SELECT DISTINCT ZS_DM,ZS_MC,XH FROM DM_YSKM ) C
        ON A.SJYSKM_DM = C.ZS_DM
        WHERE C.ZS_DM IS NOT NULL
        AND A.RQ = SUBSTR(#{req.endDate},1,4)
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.XZQH_DM='3300'
            </otherwise>
        </choose>
        AND TYPE_CODE IN ('1','2')
        GROUP BY C.ZS_DM, C.ZS_MC,C.XH
        ) T
        WHERE CASE WHEN SUBSTR(#{req.endDate},1,4) >= '2019' THEN YSKM_DM &lt;> 'SD' ELSE 1=1 END
        GROUP BY YSKM_DM,YSKM_MC,SEQ
        ORDER BY SEQ ASC
    </select>
</mapper>