<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.SeparateSubjectAndLevelMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectSeparateSubjectAndLevelProvince"
            resultType="com.zjhh.tax.statistics.vo.SeparateSubjectAndLevelVO">
        SELECT SJYSKM_DM ,YSKM_DM,YSKM_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        SUM(BQ_CENT) BQ_CENT,
        CASE WHEN SUM(SQ_CENT)=0 THEN NULL ELSE ROUND((SUM(BQ_CENT)/SUM(SQ_CENT)-1)*100,2) END CENT_ZF,
        SUM(BQ_DF) DF_AMT,
        CASE WHEN SUM(SQ_DF)=0 THEN NULL ELSE ROUND((SUM(BQ_DF)/SUM(SQ_DF)-1)*100,2) END DF_ZF,
        SUM(BQ_CITY) BQ_CITY,
        CASE WHEN SUM(SQ_CITY)=0 THEN NULL ELSE ROUND((SUM(BQ_CITY)/SUM(SQ_CITY)-1)*100,2) END CITY_ZF,
        SUM(BQ_COUNTY) BQ_COUNTY,
        CASE WHEN SUM(SQ_COUNTY)=0 THEN NULL ELSE ROUND((SUM(BQ_COUNTY)/SUM(SQ_COUNTY)-1)*100,2) END COUNTY_ZF,
        SUM(BQ_QX) BQ_QX,
        CASE WHEN SUM(SQ_QX)=0 THEN NULL ELSE ROUND((SUM(BQ_QX)/SUM(SQ_QX)-1)*100,2) END QX_ZF,
        ISLEAF
        FROM (
        WITH DM_YSKM AS (
        SELECT A.SJYSKM_DM,A.YSKM_DM, A.YSKM_MC, B.YSKM_DM CODE, A.ISLEAF
        FROM DM_GY_YSKM A ,DM_GY_YSKM_ZQ B
        WHERE A.YSKM_DM = B.SJYSKM_DM
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.SJYSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND A.SJYSKM_DM IS NULL
            </otherwise>
        </choose>
        )
        SELECT B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT,
        SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) BQ_CENT,0 SQ_CENT,
        SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) BQ_DF,0 SQ_DF,
        SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) BQ_CITY,0 SQ_CITY,
        SUM(CASE WHEN A.YSJC_DM IN('3','4','5') THEN A.YEARAMT ELSE 0 END) BQ_COUNTY,0 SQ_COUNTY,
        0 BQ_QX,0 SQ_QX,
        B.ISLEAF
        FROM ADS_TAX_INCOME_DAY A,DM_YSKM B
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.CODE
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,B.ISLEAF
        UNION ALL
        SELECT B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 BQ_CENT,SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) SQ_CENT,
        0 BQ_DF,SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) SQ_DF,
        0 BQ_CITY,SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) SQ_CITY,
        0 BQ_COUNTY,SUM(CASE WHEN A.YSJC_DM IN('3','4','5') THEN A.YEARAMT ELSE 0 END) SQ_COUNTY,
        0 BQ_QX,0 SQ_QX,
        B.ISLEAF
        FROM ADS_TAX_INCOME_MON A,DM_YSKM B
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(SUBSTR(#{req.endDate},1,6),'YYYYMM'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.CODE
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,B.ISLEAF
        ) T
        GROUP BY SJYSKM_DM,YSKM_DM,YSKM_MC,ISLEAF
        ORDER BY 2 ASC
    </select>
    <select id="selectSeparateSubjectAndLevelDistrict"
            resultType="com.zjhh.tax.statistics.vo.SeparateSubjectAndLevelVO">
        SELECT SJYSKM_DM ,YSKM_DM,YSKM_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        SUM(BQ_CENT) BQ_CENT,
        CASE WHEN SUM(SQ_CENT)=0 THEN NULL ELSE ROUND((SUM(BQ_CENT)/SUM(SQ_CENT)-1)*100,2) END CENT_ZF,
        SUM(BQ_DF) DF_AMT,
        CASE WHEN SUM(SQ_DF)=0 THEN NULL ELSE ROUND((SUM(BQ_DF)/SUM(SQ_DF)-1)*100,2) END DF_ZF,
        SUM(BQ_CITY) BQ_CITY,
        CASE WHEN SUM(SQ_CITY)=0 THEN NULL ELSE ROUND((SUM(BQ_CITY)/SUM(SQ_CITY)-1)*100,2) END CITY_ZF,
        SUM(BQ_COUNTY) BQ_COUNTY,
        CASE WHEN SUM(SQ_COUNTY)=0 THEN NULL ELSE ROUND((SUM(BQ_COUNTY)/SUM(SQ_COUNTY)-1)*100,2) END COUNTY_ZF,
        SUM(BQ_QX) BQ_QX,
        CASE WHEN SUM(SQ_QX)=0 THEN NULL ELSE ROUND((SUM(BQ_QX)/SUM(SQ_QX)-1)*100,2) END QX_ZF,
        ISLEAF
        FROM (
        WITH DM_YSKM AS (
        SELECT A.SJYSKM_DM,A.YSKM_DM, A.YSKM_MC, B.YSKM_DM CODE, A.ISLEAF
        FROM DM_GY_YSKM A ,DM_GY_YSKM_ZQ B
        WHERE A.YSKM_DM = B.SJYSKM_DM
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.SJYSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND A.SJYSKM_DM IS NULL
            </otherwise>
        </choose>
        )
        SELECT B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT,
        SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) BQ_CENT,0 SQ_CENT,
        SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) BQ_DF,0 SQ_DF,
        SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) BQ_CITY,0 SQ_CITY,
        SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) BQ_COUNTY,0 SQ_COUNTY,
        SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) BQ_QX,0 SQ_QX,
        B.ISLEAF
        FROM ADS_TAX_INCOME_QX_DAY A,DM_YSKM B
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.CODE
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,B.ISLEAF
        UNION ALL
        SELECT B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 BQ_CENT,SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) SQ_CENT,
        0 BQ_DF,SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) SQ_DF,
        0 BQ_CITY,SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) SQ_CITY,
        0 BQ_COUNTY,SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) SQ_COUNTY,
        0 BQ_QX,SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) SQ_QX,
        B.ISLEAF
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSKM B
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(SUBSTR(#{req.endDate},1,6),'YYYYMM'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.CODE
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,B.ISLEAF
        ) T
        GROUP BY SJYSKM_DM,YSKM_DM,YSKM_MC,ISLEAF
        ORDER BY 2 ASC
    </select>
    <select id="selectSeparateSubjectAndLevelCityDistrict"
            resultType="com.zjhh.tax.statistics.vo.SeparateSubjectAndLevelVO">
        SELECT SJYSKM_DM ,YSKM_DM,YSKM_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        SUM(BQ_CENT) BQ_CENT,
        CASE WHEN SUM(SQ_CENT)=0 THEN NULL ELSE ROUND((SUM(BQ_CENT)/SUM(SQ_CENT)-1)*100,2) END CENT_ZF,
        SUM(BQ_DF) DF_AMT,
        CASE WHEN SUM(SQ_DF)=0 THEN NULL ELSE ROUND((SUM(BQ_DF)/SUM(SQ_DF)-1)*100,2) END DF_ZF,
        SUM(BQ_CITY) BQ_CITY,
        CASE WHEN SUM(SQ_CITY)=0 THEN NULL ELSE ROUND((SUM(BQ_CITY)/SUM(SQ_CITY)-1)*100,2) END CITY_ZF,
        SUM(BQ_COUNTY) BQ_COUNTY,
        CASE WHEN SUM(SQ_COUNTY)=0 THEN NULL ELSE ROUND((SUM(BQ_COUNTY)/SUM(SQ_COUNTY)-1)*100,2) END COUNTY_ZF,
        SUM(BQ_QX) BQ_QX,
        CASE WHEN SUM(SQ_QX)=0 THEN NULL ELSE ROUND((SUM(BQ_QX)/SUM(SQ_QX)-1)*100,2) END QX_ZF,
        ISLEAF
        FROM (
        WITH DM_YSKM AS (
        SELECT A.SJYSKM_DM,A.YSKM_DM, A.YSKM_MC, B.YSKM_DM CODE, A.ISLEAF
        FROM DM_GY_YSKM A ,DM_GY_YSKM_ZQ B
        WHERE A.YSKM_DM = B.SJYSKM_DM
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.SJYSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND A.SJYSKM_DM IS NULL
            </otherwise>
        </choose>
        )
        SELECT B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT,
        SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) BQ_CENT,0 SQ_CENT,
        SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) BQ_DF,0 SQ_DF,
        SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) BQ_CITY,0 SQ_CITY,
        SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) BQ_COUNTY,0 SQ_COUNTY,
        SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) BQ_QX,0 SQ_QX,
        B.ISLEAF
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_YSKM B
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.CODE
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,B.ISLEAF
        UNION ALL
        SELECT B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 BQ_CENT,SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) SQ_CENT,
        0 BQ_DF,SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) SQ_DF,
        0 BQ_CITY,SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) SQ_CITY,
        0 BQ_COUNTY,SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) SQ_COUNTY,
        0 BQ_QX, SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) SQ_QX,
        B.ISLEAF
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSKM B
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(SUBSTR(#{req.endDate},1,6),'YYYYMM'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM = B.CODE
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        GROUP BY B.SJYSKM_DM,B.YSKM_DM,B.YSKM_MC,B.ISLEAF
        ) T
        GROUP BY SJYSKM_DM,YSKM_DM,YSKM_MC,ISLEAF
        ORDER BY 2 ASC
    </select>
</mapper>