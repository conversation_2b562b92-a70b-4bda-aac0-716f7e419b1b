<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.LibraryReportDayReportMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectLibraryReportDayReportProvince"
            resultType="com.zjhh.tax.statistics.vo.LibraryReportDayReportSummaryVO">
        SELECT A.XH AS RN,A.ZS_DM AS YSKM_DM,A.ZS_MC AS YSKM_MC,
        CASE WHEN SUM(BQ_YEAR) IS NULL THEN 0 ELSE ROUND(SUM(BQ_YEAR),2) END BQ_YEAR
        FROM (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '4') A
        LEFT JOIN
        ( SELECT A.YSKM_DM,SUM(A.YEARAMT) BQ_YEAR
        FROM ADS_TAX_INCOME_DAY A,(SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B
        WHERE A.RQ = #{req.endDate}
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND ZSJG_DM = '000000000000'
        GROUP BY A.YSKM_DM
        ) B ON A.DMMX = B.YSKM_DM
        GROUP BY A.ZS_DM,A.ZS_MC,A.XH
        ORDER BY XH
    </select>
    <select id="selectLibraryReportDayReportDistrict"
            resultType="com.zjhh.tax.statistics.vo.LibraryReportDayReportSummaryVO">
        SELECT A.XH AS RN,A.ZS_DM AS YSKM_DM,A.ZS_MC AS YSKM_MC,
        CASE WHEN SUM(BQ_YEAR) IS NULL THEN 0 ELSE ROUND(SUM(BQ_YEAR),2) END BQ_YEAR
        FROM (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '4') A
        LEFT JOIN
        ( SELECT A.YSKM_DM,SUM(A.YEARAMT) BQ_YEAR
        FROM ADS_TAX_INCOME_QX_DAY A,(SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B
        WHERE A.RQ = #{req.endDate}
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND ZSJG_DM = '000000000000'
        GROUP BY A.YSKM_DM
        ) B ON A.DMMX = B.YSKM_DM
        GROUP BY A.ZS_DM,A.ZS_MC,A.XH
        ORDER BY XH
    </select>
    <select id="selectLibraryReportDayReportCityDistrict"
            resultType="com.zjhh.tax.statistics.vo.LibraryReportDayReportSummaryVO">
        SELECT A.XH AS RN,A.ZS_DM AS YSKM_DM,A.ZS_MC AS YSKM_MC,
        CASE WHEN SUM(BQ_YEAR) IS NULL THEN 0 ELSE ROUND(SUM(BQ_YEAR),2) END BQ_YEAR
        FROM (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '4') A
        LEFT JOIN
        ( SELECT A.YSKM_DM,SUM(A.YEARAMT) BQ_YEAR
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,(SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0') B
        WHERE A.RQ = #{req.endDate}
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND ZSJG_DM = '000000000000'
        GROUP BY A.YSKM_DM
        ) B ON A.DMMX = B.YSKM_DM
        GROUP BY A.ZS_DM,A.ZS_MC,A.XH
        ORDER BY XH
    </select>
</mapper>