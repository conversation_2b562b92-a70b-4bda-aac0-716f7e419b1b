<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.income.dao.mapper.SeparateIndustryMapper">
    <select id="selectMajorEnterprise" resultType="com.zjhh.cz.vo.CommonMenuVO">
        select qylx_dm as key ,qylx_mc as title ,null parentKey,qylx_dm as value
        from dm_gy_sp_type t
        order by T.qylx_dm ASC, key DESC
    </select>
    <select id="selectIndustry" resultType="com.zjhh.cz.vo.CommonMenuVO">
        select hy_dm as code, hy_mc as name, NVL(sjhy_dm, '000') as parentCode
        from dm_gy_hy t
        order by code
    </select>
    <select id="selectStructProvinceZDQYIsNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryStructVO">
        SELECT A.RQ BBQ,A<PERSON>_<PERSON>,A.HY_NAME,A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY
        A.HY_CODE ORDER BY RQ
        ASC)-1)*100,2) END BQ_ZF
        FROM (
        SELECT RQ,C.SJHY_DM HY_CODE,C.SJHY_MC HY_NAME,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,NVL(C.HY_DM,'VVVV') HY_DM ,

        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,
        NVL(C.HY_DM,'VVVV') HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                AND A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        ) A LEFT JOIN DM_GY_HY_ZQ C ON A.HY_DM = C.HY_DM
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                C.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJHY_DM IN('1','2','3')
            </otherwise>
        </choose>
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC) A
        ORDER BY A.RQ,A.HY_CODE
    </select>
    <select id="selectStructDistrictZDQYIsNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryStructVO">
        SELECT A.RQ BBQ,A.HY_CODE,A.HY_NAME,A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY
        A.HY_CODE ORDER BY RQ
        ASC)-1)*100,2) END BQ_ZF
        FROM (
        SELECT RQ,C.SJHY_DM HY_CODE,C.SJHY_MC HY_NAME,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,NVL(C.HY_DM,'VVVV') HY_DM ,
        <if test="req.ysjcStr ==  null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ,5,2) RQ,
        NVL(C.HY_DM,'VVVV') HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                AND A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        ) A LEFT JOIN DM_GY_HY_ZQ C ON A.HY_DM = C.HY_DM
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                C.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJHY_DM IN('1','2','3')
            </otherwise>
        </choose>
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC) A
        ORDER BY A.RQ,A.HY_CODE
    </select>
    <select id="selectStructCityDistrictZDQYIsNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryStructVO">
        SELECT A.RQ BBQ,A.HY_CODE,A.HY_NAME,A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY
        A.HY_CODE ORDER BY RQ
        ASC)-1)*100,2) END BQ_ZF
        FROM (
        SELECT RQ,C.SJHY_DM HY_CODE,C.SJHY_MC HY_NAME,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,NVL(C.HY_DM,'VVVV') HY_DM ,

        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* ,0 AS QX_AMT
        FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ,5,2) RQ,
        NVL(C.HY_DM,'VVVV') HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* ,0 AS QX_AMT
        FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                AND A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        ) A LEFT JOIN DM_GY_HY_ZQ C ON A.HY_DM = C.HY_DM
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                C.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJHY_DM IN('1','2','3')
            </otherwise>
        </choose>
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC) A
        ORDER BY A.RQ,A.HY_CODE
    </select>
    <select id="selectStructProvinceZDQYIsNotNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryStructVO">
        SELECT A.RQ BBQ,A.HY_CODE,A.HY_NAME,A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY
        A.HY_CODE ORDER BY RQ
        ASC)-1)*100,2) END BQ_ZF
        FROM (
        SELECT RQ,C.SJHY_DM HY_CODE,C.SJHY_MC HY_NAME,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,NVL(C.HY_DM,'VVVV') HY_DM,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        WITH ADS_SPQY AS (
        SELECT DISTINCT QYID FROM ADS_SAT_SP_QYXX A
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND QYLX_DM IN
        <foreach collection="req.zdqy" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT RQ, A.HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM
        (SELECT A.* FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE

        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        )A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,
        A.HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM
        (SELECT A.* FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE

        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        )
        A, ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                AND A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        ) A LEFT JOIN DM_GY_HY_ZQ C ON A.HY_DM = C.HY_DM
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                C.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJHY_DM IN('1','2','3')
            </otherwise>
        </choose>
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC) A
        ORDER BY A.RQ,A.HY_CODE
    </select>
    <select id="selectStructDistrictZDQYIsNotNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryStructVO">
        SELECT A.RQ BBQ,A.HY_CODE,A.HY_NAME,A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY
        A.HY_CODE ORDER BY RQ
        ASC)-1)*100,2) END BQ_ZF
        FROM (
        SELECT RQ,C.SJHY_DM HY_CODE,C.SJHY_MC HY_NAME,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,NVL(C.HY_DM,'VVVV') HY_DM,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        WITH ADS_SPQY AS (
        SELECT DISTINCT QYID FROM ADS_SAT_SP_QYXX A
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND QYLX_DM IN
        <foreach collection="req.zdqy" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT RQ, A.HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,
        A.HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A, ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                AND A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        ) A LEFT JOIN DM_GY_HY_ZQ C ON A.HY_DM = C.HY_DM
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                C.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJHY_DM IN('1','2','3')
            </otherwise>
        </choose>
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC) A
        ORDER BY A.RQ,A.HY_CODE
    </select>
    <select id="selectStructCityDistrictZDQYIsNotNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryStructVO">
        SELECT A.RQ BBQ,A.HY_CODE,A.HY_NAME,A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_CODE ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY
        A.HY_CODE ORDER BY RQ
        ASC)-1)*100,2) END BQ_ZF
        FROM (
        SELECT RQ,C.SJHY_DM HY_CODE,C.SJHY_MC HY_NAME,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,NVL(C.HY_DM,'VVVV') HY_DM,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        WITH ADS_SPQY AS (
        SELECT DISTINCT QYID FROM ADS_SAT_SP_QYXX A
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND QYLX_DM IN
        <foreach collection="req.zdqy" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT RQ, A.HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.*,0 AS QX_AMT FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ,5,2) RQ,
        A.HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.*,0 AS QX_AMT FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A, ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                AND A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) C ON A.HY_DM = C.HY_DM
        GROUP BY RQ,NVL(C.HY_DM,'VVVV')
        ) A LEFT JOIN DM_GY_HY_ZQ C ON A.HY_DM = C.HY_DM
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                C.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                C.SJHY_DM IN('1','2','3')
            </otherwise>
        </choose>
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC) A
        ORDER BY A.RQ,A.HY_CODE
    </select>
    <select id="selectTableProvinceZDQYIsNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryTableVO">

        SELECT A.RQ BBQ,SJHY_DM PARENTID,A.HY_DM,A.HYMC,
        A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM
        ORDER BY A.RQ ASC)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(A.HY_DM)=4 OR A.HY_DM='0' THEN 1 ELSE 0 END ISLEAF,
        #{req.endDate} MAX_DT
        FROM (
        SELECT RQ,SJHY_DM,SJHY_MC,NVL(HY_DM,'0')HY_DM,NVL(HYMC,'合计') HYMC,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ, C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) AS SQ_AMT
        FROM (
        SELECT RQ,NVL(B.HY_DM,'VVVV') HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(B.HY_DM,'VVVV')
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,
        NVL(B.HY_DM,'VVVV') HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(B.HY_DM,'VVVV')
        ) A ,
        (SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC HYMC, B.HY_DM HY_DM1
        FROM DM_GY_HY A, DM_GY_HY_ZQ B
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                A.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.SJHY_DM IN('0')
            </otherwise>
        </choose>
        AND A.HY_DM = B.SJHY_DM
        ) C
        WHERE A.HY_DM = C.HY_DM1
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC
        ) T GROUP BY ROLLUP(T.RQ,(T.SJHY_DM,T.SJHY_MC,T.HY_DM,T.HYMC))
        ) A WHERE A.RQ IS NOT NULL
        ORDER BY A.RQ,A.SJHY_DM,A.HY_DM
    </select>
    <select id="selectTableDistrictZDQYIsNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryTableVO">

        SELECT A.RQ BBQ,SJHY_DM PARENTID,A.HY_DM,A.HYMC,
        A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM
        ORDER BY A.RQ ASC)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(A.HY_DM)=4 OR A.HY_DM='0' THEN 1 ELSE 0 END ISLEAF,
        #{req.endDate} MAX_DT
        FROM (
        SELECT T.RQ,T.SJHY_DM,T.SJHY_MC,NVL(T.HY_DM,'0')HY_DM,NVL(T.HYMC,'合计') HYMC,
        SUM(T.BQ_AMT) BQ_AMT, SUM(T.SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ, C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) AS SQ_AMT
        FROM (
        SELECT RQ,NVL(B.HY_DM,'VVVV') HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(B.HY_DM,'VVVV')
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,
        NVL(B.HY_DM,'VVVV') HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(B.HY_DM,'VVVV')
        ) A ,
        (SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC HYMC, B.HY_DM HY_DM1
        FROM DM_GY_HY A, DM_GY_HY_ZQ B
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                A.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.SJHY_DM IN('0')
            </otherwise>
        </choose>
        AND A.HY_DM = B.SJHY_DM
        ) C
        WHERE A.HY_DM = C.HY_DM1
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC
        ) T GROUP BY ROLLUP(T.RQ,(T.SJHY_DM,T.SJHY_MC,T.HY_DM,T.HYMC))
        ) A WHERE A.RQ IS NOT NULL
        ORDER BY A.RQ,A.SJHY_DM,A.HY_DM
    </select>
    <select id="selectTableCityDistrictZDQYIsNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryTableVO">

        SELECT A.RQ BBQ,SJHY_DM PARENTID,A.HY_DM,A.HYMC,
        A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM
        ORDER BY A.RQ ASC)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(A.HY_DM)=4 OR A.HY_DM='0' THEN 1 ELSE 0 END ISLEAF,
        #{req.endDate} MAX_DT
        FROM (
        SELECT RQ,SJHY_DM,SJHY_MC,NVL(HY_DM,'0')HY_DM,NVL(HYMC,'合计') HYMC,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ, C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) AS SQ_AMT
        FROM (
        SELECT RQ,NVL(B.HY_DM,'VVVV') HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* ,0 AS QX_AMT
        FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(B.HY_DM,'VVVV')
        UNION ALL
        SELECT SUBSTR(#{req.endDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,
        NVL(B.HY_DM,'VVVV') HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* ,0 AS QX_AMT
        FROM ADS_SAT_HZXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_HZXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A
        LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,NVL(B.HY_DM,'VVVV')
        ) A ,
        (SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC HYMC, B.HY_DM HY_DM1
        FROM DM_GY_HY A, DM_GY_HY_ZQ B
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                A.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.SJHY_DM IN('0')
            </otherwise>
        </choose>
        AND A.HY_DM = B.SJHY_DM
        ) C
        WHERE A.HY_DM = C.HY_DM1
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC
        ) T GROUP BY ROLLUP(T.RQ,(T.SJHY_DM,T.SJHY_MC,T.HY_DM,T.HYMC))
        ) A WHERE A.RQ IS NOT NULL
        ORDER BY A.RQ,A.SJHY_DM,A.HY_DM
    </select>
    <select id="selectTableProvinceZDQYIsNotNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryTableVO">


        SELECT A.RQ BBQ,SJHY_DM PARENTID,A.HY_DM,A.HYMC,
        A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM
        ORDER BY A.RQ ASC)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(A.HY_DM)=4 OR A.HY_DM='0' THEN 1 ELSE 0 END ISLEAF,
        #{req.endDate} MAX_DT
        FROM (
        SELECT RQ,SJHY_DM,SJHY_MC,NVL(HY_DM,'0')HY_DM,NVL(HYMC,'合计') HYMC,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ, NVL(B.HY_DM,'VVVV') HY_DM,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) SQ_AMT
        FROM (
        WITH ADS_SPQY AS (
        SELECT DISTINCT QYID FROM ADS_SAT_SP_QYXX A
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND QYLX_DM IN
        <foreach collection="req.zdqy" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT RQ,A.HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM
        (SELECT A.* FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE

        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,A.HY_DM
        UNION ALL
        SELECT SUBSTR(#{req.startDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,A.HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE

        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        ) A LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        GROUP BY RQ, NVL(B.HY_DM,'VVVV')
        ) A ,
        (SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC HYMC, B.HY_DM HY_DM1
        FROM DM_GY_HY A, DM_GY_HY_ZQ B
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                A.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.SJHY_DM IN('0')
            </otherwise>
        </choose>
        AND A.HY_DM = B.SJHY_DM) C
        WHERE A.HY_DM = C.HY_DM1
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC
        ) T GROUP BY ROLLUP(T.RQ,(T.SJHY_DM,T.SJHY_MC,T.HY_DM,T.HYMC))
        ) A WHERE A.RQ IS NOT NULL
        ORDER BY A.RQ,A.SJHY_DM,A.HY_DM
    </select>
    <select id="selectTableDistrictZDQYIsNotNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryTableVO">


        SELECT A.RQ BBQ,SJHY_DM PARENTID,A.HY_DM,A.HYMC,
        A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM
        ORDER BY A.RQ ASC)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(A.HY_DM)=4 OR A.HY_DM='0' THEN 1 ELSE 0 END ISLEAF,
        #{req.endDate} MAX_DT
        FROM (
        SELECT RQ,SJHY_DM,SJHY_MC,NVL(HY_DM,'0')HY_DM,NVL(HYMC,'合计') HYMC,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ, NVL(B.HY_DM,'VVVV') HY_DM,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) SQ_AMT
        FROM (
        WITH ADS_SPQY AS (
        SELECT DISTINCT QYID FROM ADS_SAT_SP_QYXX A
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND QYLX_DM IN
        <foreach collection="req.zdqy" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT RQ,A.HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,A.HY_DM
        UNION ALL
        SELECT SUBSTR(#{req.startDate}, 1, 4) || SUBSTR(RQ, 5,2) RQ,A.HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.* FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        ) A LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        GROUP BY RQ, NVL(B.HY_DM,'VVVV')
        ) A ,
        (SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC HYMC, B.HY_DM HY_DM1
        FROM DM_GY_HY A, DM_GY_HY_ZQ B
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                A.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.SJHY_DM IN('0')
            </otherwise>
        </choose>
        AND A.HY_DM = B.SJHY_DM) C
        WHERE A.HY_DM = C.HY_DM1
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC
        ) T GROUP BY ROLLUP(T.RQ,(T.SJHY_DM,T.SJHY_MC,T.HY_DM,T.HYMC))
        ) A WHERE A.RQ IS NOT NULL
        ORDER BY A.RQ,A.SJHY_DM,A.HY_DM
    </select>
    <select id="selectTableCityDistrictZDQYIsNotNull" resultType="com.zjhh.tax.income.vo.SeparateIndustryTableVO">

        SELECT A.RQ BBQ,SJHY_DM PARENTID,A.HY_DM,A.HYMC,
        A.BQ_AMT,A.SQ_AMT,
        SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) BQLJ_AMT,
        SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC) SQLJ_AMT,
        CASE WHEN A.SQ_AMT=0 THEN NULL ELSE ROUND((A.BQ_AMT/A.SQ_AMT-1)*100,2) END BY_ZF,
        CASE WHEN SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)=0 THEN NULL
        ELSE ROUND((SUM(A.BQ_AMT) OVER(PARTITION BY A.HY_DM ORDER BY A.RQ ASC)/SUM(A.SQ_AMT) OVER(PARTITION BY A.HY_DM
        ORDER BY A.RQ ASC)-1)*100,2) END BQ_ZF,
        CASE WHEN LENGTH(A.HY_DM)=4 OR A.HY_DM='0' THEN 1 ELSE 0 END ISLEAF,
        #{req.endDate} MAX_DT
        FROM (
        SELECT RQ,SJHY_DM,SJHY_MC,NVL(HY_DM,'0')HY_DM,NVL(HYMC,'合计') HYMC,
        SUM(BQ_AMT) BQ_AMT, SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) SQ_AMT
        FROM (
        SELECT RQ, NVL(B.HY_DM,'VVVV') HY_DM,
        SUM(BQ_AMT) AS BQ_AMT,SUM(SQ_AMT) SQ_AMT
        FROM (
        WITH ADS_SPQY AS (
        SELECT DISTINCT QYID FROM ADS_SAT_SP_QYXX A
        WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND QYLX_DM IN
        <foreach collection="req.zdqy" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        SELECT RQ,A.HY_DM,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        BQ_AMT, 0 SQ_AMT
        FROM (SELECT A.*,0 AS QX_AMT FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        GROUP BY RQ,A.HY_DM
        UNION ALL
        SELECT SUBSTR(#{req.startDate}, 1, 4) || SUBSTR(RQ,5,2) RQ,A.HY_DM,
        0 BQ_AMT,
        <if test="req.ysjcStr == null">
            SUM(AMT)
        </if>
        <if test="req.ysjcStr == '1'.toString()">
            SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '2'.toString()">
            SUM(CITY_AMT)
        </if>
        <if test="req.ysjcStr == '3,4,5'.toString()">
            SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '6'.toString()">
            SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '1,2'.toString()">
            SUM(CITY_AMT)+SUM(CENT_AMT)
        </if>
        <if test="req.ysjcStr == '1,3,4,5'.toString()">
            SUM(CENT_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '1,6'.toString()">
            SUM(CENT_AMT)+SUM(QX_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)
        </if>
        <if test="req.ysjcStr == '2,3,4,5,6'.toString()">
            SUM(CITY_AMT)+SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '2,6'.toString()">
            SUM(CITY_AMT)+SUM(QX_AMT)
        </if>

        <if test="req.ysjcStr == '3,4,5,6'.toString()">
            SUM(COUNTY_AMT)+SUM(QX_AMT)
        </if>
        SQ_AMT
        FROM (SELECT A.*,0 AS QX_AMT FROM ADS_SAT_QYXX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        UNION ALL
        SELECT A.*
        FROM ADS_SAT_QYXX_QX_MON A , (SELECT * FROM DM_CZ_XZQH A WHERE
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        ) B
        WHERE (A.RQ BETWEEN #{req.startDate} AND #{req.endDate}
        OR A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt})
        AND A.XZQH_DM = B.XZQH_DM
        ) A,ADS_SPQY E
        WHERE A.RQ BETWEEN #{req.sqStartDt} AND #{req.sqEndDt}
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                AND A.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.QYID=E.QYID
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                AND A.GDSBS IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        AND A.YSKM_DM NOT LIKE 'AA1010103%'
        GROUP BY RQ,A.HY_DM
        ) A LEFT JOIN (SELECT * FROM DM_GY_HY A WHERE LENGTH(HY_DM) = 4 ) B ON A.HY_DM = B.HY_DM
        GROUP BY RQ, NVL(B.HY_DM,'VVVV')
        ) A ,
        (SELECT A.SJHY_DM, A.SJHY_MC, A.HY_DM, A.HY_MC HYMC, B.HY_DM HY_DM1
        FROM DM_GY_HY A, DM_GY_HY_ZQ B
        WHERE
        <choose>
            <when test="req.hydm != null and req.hydm.size > 0">
                A.SJHY_DM IN
                <foreach collection="req.hydm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.SJHY_DM IN('0')
            </otherwise>
        </choose>
        AND A.HY_DM = B.SJHY_DM) C
        WHERE A.HY_DM = C.HY_DM1
        GROUP BY RQ,C.SJHY_DM,C.SJHY_MC,C.HY_DM,C.HYMC
        ) T GROUP BY ROLLUP(T.RQ,(T.SJHY_DM,T.SJHY_MC,T.HY_DM,T.HYMC))
        ) A WHERE A.RQ IS NOT NULL
        ORDER BY A.RQ,A.SJHY_DM,A.HY_DM
    </select>
    <select id="selectIndustryMaxDate" resultType="java.lang.String">
        SELECT MAX(RQ)
        FROM ADS_SAT_HZXX_MON A
        WHERE SUBSTR(A.RQ, 1, 4) = #{endDate}
    </select>

</mapper>