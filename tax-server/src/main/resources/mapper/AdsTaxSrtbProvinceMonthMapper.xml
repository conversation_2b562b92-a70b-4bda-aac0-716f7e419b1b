<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.tax.datamanagement.dao.mapper.AdsTaxSrtbProvinceMonthMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , srtb_province_id, xzqh_dm, ss_bhmdt_amt, ss_ldts_amt, ss_blmdt_amt, ss_sqmdt_amt, fs_amt, bq_yb_amt, bq_ss_amt, bq_fs_amt
    </sql>

    <select id="findTbDetail" resultType="com.zjhh.tax.datamanagement.vo.TbVo">
        select count(t1.xzqh_dm) as need_tb, count(t2.xzqh_dm) as had_tb
        from dm_cz_xzqh t1
                 left join ads_tax_srtb_province_month t2
                           on t1.xzqh_dm = t2.xzqh_dm and t2.datekey = #{datekey} and t2.batch_no = #{batchNo}
        where t1.sqbz = '1'
          AND t1.sbbz = '1'
    </select>

    <select id="listBackXzqh" resultType="com.zjhh.tax.datamanagement.vo.srfsdwcqk.BackXzqhVo">
        select t1.xzqh_dm, t1.xzqh_mc
        from dm_cz_xzqh t1
        where sqbz = '1'
          AND sbbz = '1'
        order by t1.xzqh_dm
    </select>

    <select id="listProcessTrack" resultType="com.zjhh.tax.datamanagement.vo.srfsdwcqk.ProcessTrackVo">
        select t1.xzqh_dm,
               t1.xzqh_mc,
               case
                   when t2.xzqh_dm is null
                       then 0
                   else 1 end as state,
               t2.submit_time
        from dm_cz_xzqh t1
                 left join ads_tax_srtb_province_month t2
                           on t1.xzqh_dm = t2.xzqh_dm and t2.datekey = #{datekey} and t2.batch_no = #{batchNo}
        where t1.sqbz = '1'
          AND t1.sbbz = '1'
    </select>

    <select id="listSrfsdwcqkDetail" resultType="com.zjhh.tax.datamanagement.vo.srfsdwcqk.SrfsdwcqkDetailVo">
        SELECT t1.xzqh_dm,
               t3.xzqh_mc,
               t1.datekey,
               t2.batch_no,
               COALESCE(t2.ss_bhmdt_amt, 0) + COALESCE(t2.ss_ldts_amt, 0) +
               COALESCE(t2.ss_sqmdt_amt, 0) + COALESCE(t2.fs_amt, 0)     AS yb_amt,
               COALESCE(t2.ss_bhmdt_amt, 0) + COALESCE(t2.ss_ldts_amt, 0) +
               COALESCE(t2.ss_sqmdt_amt, 0)                              AS ss_amt,
               COALESCE(t2.ss_bhmdt_amt, 0)                              AS ss_bhmdt_amt,
               COALESCE(t2.ss_ldts_amt, 0)                               AS ss_ldts_amt,
               COALESCE(t2.ss_blmdt_amt, 0)                              AS ss_blmdt_amt,
               COALESCE(t2.ss_sqmdt_amt, 0)                              AS ss_sqmdt_amt,
               COALESCE(t2.fs_amt, 0)                                    AS fs_amt,
               COALESCE(t2.ss_bhmdt_amt, 0) + COALESCE(t2.ss_ldts_amt, 0) +
               COALESCE(t2.ss_sqmdt_amt, 0) + COALESCE(t2.fs_amt, 0) +
               COALESCE(t1.syq_yb_amt, 0)                                AS yb_lj_amt,
               COALESCE(t2.ss_bhmdt_amt, 0) + COALESCE(t2.ss_ldts_amt, 0) +
               COALESCE(t2.ss_sqmdt_amt, 0) + COALESCE(t1.syq_ss_amt, 0) AS ss_lj_amt,
               COALESCE(t2.fs_amt, 0) + COALESCE(t1.syq_fs_amt, 0)       as fs_lj_amt,
               COALESCE(t1.sqy_yb_amt, 0)                                AS sqy_yb_amt,
               COALESCE(t1.sqy_ss_amt, 0)                                AS sqy_ss_amt,
               COALESCE(t1.sqy_fs_amt, 0)                                AS sqy_fs_amt,
               COALESCE(t1.syq_yb_amt, 0)                                AS syq_yb_amt,
               COALESCE(t1.syq_ss_amt, 0)                                AS syq_ss_amt,
               COALESCE(t1.syq_fs_amt, 0)                                AS syq_fs_amt,
               COALESCE(t1.sq_yb_amt, 0)                                 AS sq_yb_amt,
               COALESCE(t1.sq_ss_amt, 0)                                 AS sq_ss_amt,
               COALESCE(t1.sq_fs_amt, 0)                                 AS sq_fs_amt
        FROM ads_tax_srtb_province_annex t1
                 LEFT JOIN ads_tax_srtb_province_month t2
                           ON t1.xzqh_dm = t2.xzqh_dm AND t1.datekey = t2.datekey and batch_no = #{batchNo}
                 LEFT JOIN dm_transform_xzqh t3 ON t3.xzqh_dm = t1.xzqh_dm
        where t1.datekey = #{datekey}
        order by t3.xh
    </select>

    <select id="getSjtzs" resultType="com.zjhh.tax.datamanagement.vo.srfsdwcqk.SrfsdwcqkDetailVo">
        select COALESCE(t1.ss_bhmdt_amt, 0) as ss_bhmdt_amt,
               COALESCE(t1.ss_sqmdt_amt, 0) as ss_sqmdt_amt,
               COALESCE(t1.fs_amt, 0)       as fs_amt,
               COALESCE(t2.syq_yb_amt, 0)   as syq_yb_amt,
               COALESCE(t2.syq_ss_amt, 0)   as syq_ss_amt,
               COALESCE(t2.syq_fs_amt, 0)   as syq_fs_amt,
               COALESCE(t2.sqy_yb_amt, 0)   as sqy_yb_amt,
               COALESCE(t2.sqy_ss_amt, 0)   as sqy_ss_amt,
               COALESCE(t2.sqy_fs_amt, 0)   as sqy_fs_amt,
               COALESCE(t2.sq_yb_amt, 0)    as sq_yb_amt,
               COALESCE(t2.sq_ss_amt, 0)    as sq_ss_amt,
               COALESCE(t2.sq_fs_amt, 0)    as sq_fs_amt
        from ads_tax_srtb_province_month t1
                 left join ads_tax_srtb_province_annex t2 on t1.datekey = t2.datekey and t1.xzqh_dm = t2.xzqh_dm
        where t1.datekey = #{datekey}
          and t1.batch_no = #{batchNo}
          and t1.xzqh_dm = #{xzqhDm}
    </select>

    <select id="listSrfsdwcqkJcDetail"
            resultType="com.zjhh.tax.datamanagement.vo.srfsdwcqk.SrfsdwcqkJcDetailVo">
        select b.xzqh_dm,
               b.xzqh_mc,
               COALESCE(by_yb_amt, 0)       ybAmt,
               COALESCE(by_yb_zf, 0)        ybZf,
               COALESCE(by_ss_amt, 0)       ssAmt,
               COALESCE(by_ss_zf, 0)        ssZf,
               COALESCE(by_ss_bhmdt_amt, 0) ssBhmdtAmt,
               COALESCE(by_ss_sqmdt_amt, 0) ssSqmdtAmt,
               COALESCE(by_fs_amt, 0)       fsAmt,
               COALESCE(by_fs_zf, 0)        fsZf,
               COALESCE(bq_yb_amt, 0)       ybLjAmt,
               COALESCE(bq_yb_zf, 0)        ybLjBsZf,
               COALESCE(bq_ss_amt, 0)       ssLjAmt,
               COALESCE(bq_ss_zf, 0)        ssLjBsZf,
               COALESCE(bq_fs_amt, 0)       fsLjAmt,
               COALESCE(bq_fs_zf, 0)        fsLjBsZf
        from (with data1 as (SELECT *
                             FROM ads_tax_srtb_df a
                             where datekey = #{datekey}
                               and batch_no = #{batchNo}),
                   data2 as (select a.srtb_df_id,
                                    a.xzqh_dm,
                                    a.yb_amt       by_yb_amt,
                                    a.yb_zf as     by_yb_zf,
                                    a.ss_amt       by_ss_amt,
                                    a.ss_zf as     by_ss_zf,
                                    a.ss_bhmdt_amt by_ss_bhmdt_amt,
                                    a.ss_sqmdt_amt by_ss_sqmdt_amt,
                                    a.fs_amt       by_fs_amt,
                                    a.fs_zf        by_fs_zf,
                                    b.yb_amt       bq_yb_amt,
                                    b.yb_zf as     bq_yb_zf,
                                    b.ss_amt       bq_ss_amt,
                                    b.ss_zf as     bq_ss_zf,
                                    b.fs_amt       bq_fs_amt,
                                    b.fs_zf        bq_fs_zf
                             from ads_tax_srtb_df_month a
                                      inner join ads_tax_srtb_df_total b
                                                 on a.srtb_df_id = b.srtb_df_id and a.xzqh_dm = b.xzqh_dm
                             where length(a.xzqh_dm) = 4),
                   data3 as (SELECT a.xzqh_dm,
                                    COALESCE(a.ss_bhmdt_amt, 0) + COALESCE(a.ss_sqmdt_amt, 0) +
                                    COALESCE(a.fs_amt, 0)                             AS yb_amt,
                                    COALESCE(a.ss_bhmdt_amt, 0) +
                                    COALESCE(a.ss_sqmdt_amt, 0)                       AS ss_amt,
                                    COALESCE(a.ss_bhmdt_amt, 0)                       AS ss_bhmdt_amt,
                                    COALESCE(a.ss_blmdt_amt, 0)                       AS ss_blmdt_amt,
                                    COALESCE(a.ss_sqmdt_amt, 0)                       AS ss_sqmdt_amt,
                                    COALESCE(a.fs_amt, 0)                             AS fs_amt,
                                    COALESCE(a.ss_bhmdt_amt, 0) + COALESCE(a.ss_sqmdt_amt, 0) + COALESCE(a.fs_amt, 0) +
                                    COALESCE(b.syq_yb_amt, 0)                         AS yb_lj_amt,
                                    COALESCE(a.ss_bhmdt_amt, 0) + COALESCE(a.ss_sqmdt_amt, 0) +
                                    COALESCE(b.syq_ss_amt, 0)                         AS ss_lj_amt,
                                    COALESCE(a.fs_amt, 0) + COALESCE(b.syq_fs_amt, 0) as fs_lj_amt,
                                    COALESCE(b.sqy_yb_amt, 0)                         AS sqy_yb_amt,
                                    COALESCE(b.sqy_ss_amt, 0)                         AS sqy_ss_amt,
                                    COALESCE(b.sqy_fs_amt, 0)                         AS sqy_fs_amt,
                                    COALESCE(b.syq_yb_amt, 0)                         AS syq_yb_amt,
                                    COALESCE(b.syq_ss_amt, 0)                         AS syq_ss_amt,
                                    COALESCE(b.syq_fs_amt, 0)                         AS syq_fs_amt,
                                    COALESCE(b.sq_yb_amt, 0)                          AS sq_yb_amt,
                                    COALESCE(b.sq_ss_amt, 0)                          AS sq_ss_amt,
                                    COALESCE(b.sq_fs_amt, 0)                          AS sq_fs_amt
                             FROM ads_tax_srtb_province_month a
                                      inner join ads_tax_srtb_province_annex b
                                                 on a.xzqh_dm = b.xzqh_dm and a.datekey = b.datekey
                             where a.datekey = #{datekey}
                               and a.batch_no = #{batchNo}
                               and a.xzqh_dm = '3302')
              select a.xzqh_dm,
                     by_yb_amt,
                     by_yb_zf,
                     by_ss_amt,
                     by_ss_zf,
                     by_ss_bhmdt_amt,
                     by_ss_sqmdt_amt,
                     by_fs_amt,
                     by_fs_zf,
                     bq_yb_amt,
                     bq_yb_zf,
                     bq_ss_amt,
                     bq_ss_zf,
                     bq_fs_amt,
                     bq_fs_zf
              from data2 a
                       inner join data1 b on a.srtb_df_id = b.id
              where length(a.xzqh_dm) = 4
              union all
              select a.xzqh_dm,
                     yb_amt                                                                              by_yb_amt,
                     case when sqy_yb_amt = 0 then 0 else round((yb_amt / sqy_yb_amt - 1) * 100, 2) end  by_yb_zf,
                     ss_amt                                                                              by_ss_amt,
                     case when sqy_ss_amt = 0 then 0 else round((ss_amt / sqy_ss_amt - 1) * 100, 2) end  by_ss_zf,
                     ss_bhmdt_amt                                                                        by_ss_bhmdt_amt,
                     ss_sqmdt_amt                                                                        by_ss_sqmdt_amt,
                     fs_amt                                                                              by_fs_amt,
                     case when sqy_fs_amt = 0 then 0 else round((fs_amt / sqy_fs_amt - 1) * 100, 2) end  by_fs_zf,
                     yb_lj_amt                                                                           bq_yb_amt,
                     case when sq_yb_amt = 0 then 0 else round((yb_lj_amt / sq_yb_amt - 1) * 100, 2) end bq_yb_zf,
                     ss_lj_amt                                                                           bq_ss_amt,
                     case when sq_ss_amt = 0 then 0 else round((ss_lj_amt / sq_ss_amt - 1) * 100, 2) end bq_ss_zf,
                     fs_lj_amt                                                                           bq_fs_amt,
                     case when sq_fs_amt = 0 then 0 else round((fs_lj_amt / sq_fs_amt - 1) * 100, 2) end bq_fs_zf
              from data3 a) a
                 right JOIN
             (select * from dm_transform_xzqh where xh is not null and xh not in ('1', '2', '3')) b
             ON a.xzqh_dm = b.xzqh_dm
        order by b.xh
    </select>

</mapper>
