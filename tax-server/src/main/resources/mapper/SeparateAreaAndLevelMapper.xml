<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.SeparateAreaAndLevelMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectSeparateAreaAndLevelProvince" resultType="com.zjhh.tax.statistics.vo.SeparateAreaAndLevelVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        SUM(BQ_CENT) BQ_CENT,
        CASE WHEN SUM(SQ_CENT)=0 THEN NULL ELSE ROUND((SUM(BQ_CENT)/SUM(SQ_CENT)-1)*100,2) END CENT_ZF,
        SUM(BQ_DF) BQ_DF,
        CASE WHEN SUM(SQ_DF)=0 THEN NULL ELSE ROUND((SUM(BQ_DF)/SUM(SQ_DF)-1)*100,2) END DF_ZF,
        SUM(BQ_CITY) BQ_CITY,
        CASE WHEN SUM(SQ_CITY)=0 THEN NULL ELSE ROUND((SUM(BQ_CITY)/SUM(SQ_CITY)-1)*100,2) END CITY_ZF,
        SUM(BQ_COUNTY) BQ_COUNTY,
        CASE WHEN SUM(SQ_COUNTY)=0 THEN NULL ELSE ROUND((SUM(BQ_COUNTY)/SUM(SQ_COUNTY)-1)*100,2) END COUNTY_ZF,
        SUM(BQ_QX) BQ_QX,
        CASE WHEN SUM(SQ_QX)=0 THEN NULL ELSE ROUND((SUM(BQ_QX)/SUM(SQ_QX)-1)*100,2) END QX_ZF,
        CASE WHEN LENGTH(XZQH_DM) = 6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM

        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="req.province != null and req.province == '1'.toString()">
                        (A.SJXZQH_DM IS NULL)
                    </when>
                    <otherwise>
                        (
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.SJXZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        OR
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.XZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        )
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        ),
        DM_YSKM AS (
        SELECT DISTINCT T.YSKM_DM
        FROM DM_GY_YSKM_ZQ T
        WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                T.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND T.SJYSKM_DM IN ('A','B')
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT,
        SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) BQ_CENT,0 SQ_CENT,
        SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) BQ_DF,0 SQ_DF,
        SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) BQ_CITY,0 SQ_CITY,
        SUM(CASE WHEN A.YSJC_DM IN('3','4','5') THEN A.YEARAMT ELSE 0 END) BQ_COUNTY,0 SQ_COUNTY,
        0 BQ_QX,0 SQ_QX
        FROM ADS_TAX_INCOME_DAY A, DM_YSKM B,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM1
        AND A.YSKM_DM = B.YSKM_DM
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 BQ_CENT,SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) SQ_CENT,
        0 BQ_DF,SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) SQ_DF,
        0 BQ_CITY,SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) SQ_CITY,
        0 BQ_COUNTY,SUM(CASE WHEN A.YSJC_DM IN('3','4','5') THEN A.YEARAMT ELSE 0 END) SQ_COUNTY,
        0 BQ_QX,0 SQ_QX
        FROM ADS_TAX_INCOME_MON A, DM_YSKM B,DM_XZQH C
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM1
        AND A.YSKM_DM = B.YSKM_DM
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC
        ) T
        GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC
        ORDER BY 2 ASC
    </select>
    <select id="selectSeparateAreaAndLevelDistrict" resultType="com.zjhh.tax.statistics.vo.SeparateAreaAndLevelVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        SUM(BQ_CENT) BQ_CENT,
        CASE WHEN SUM(SQ_CENT)=0 THEN NULL ELSE ROUND((SUM(BQ_CENT)/SUM(SQ_CENT)-1)*100,2) END CENT_ZF,
        SUM(BQ_DF) BQ_DF,
        CASE WHEN SUM(SQ_DF)=0 THEN NULL ELSE ROUND((SUM(BQ_DF)/SUM(SQ_DF)-1)*100,2) END DF_ZF,
        SUM(BQ_CITY) BQ_CITY,
        CASE WHEN SUM(SQ_CITY)=0 THEN NULL ELSE ROUND((SUM(BQ_CITY)/SUM(SQ_CITY)-1)*100,2) END CITY_ZF,
        SUM(BQ_COUNTY) BQ_COUNTY,
        CASE WHEN SUM(SQ_COUNTY)=0 THEN NULL ELSE ROUND((SUM(BQ_COUNTY)/SUM(SQ_COUNTY)-1)*100,2) END COUNTY_ZF,
        SUM(BQ_QX) BQ_QX,
        CASE WHEN SUM(SQ_QX)=0 THEN NULL ELSE ROUND((SUM(BQ_QX)/SUM(SQ_QX)-1)*100,2) END QX_ZF,
        CASE WHEN LENGTH(XZQH_DM) = 6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        -- 行政区划 1
        AND 1=1
        AND
        (
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        OR
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        )
        ),
        DM_YSKM AS (
        SELECT DISTINCT T.YSKM_DM
        FROM DM_GY_YSKM_ZQ T
        WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                T.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND T.SJYSKM_DM IN ('A','B')
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT,
        SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) BQ_CENT,0 SQ_CENT,
        SUM(CASE WHEN A.YSJC_DM &lt;>'1' THEN A.YEARAMT ELSE 0 END) BQ_DF,0 SQ_DF,
        SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) BQ_CITY,0 SQ_CITY,
        SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) BQ_COUNTY,0 SQ_COUNTY,
        SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) BQ_QX,0 SQ_QX
        FROM ADS_TAX_INCOME_QX_DAY A, DM_YSKM B,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM1
        AND A.YSKM_DM = B.YSKM_DM
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 BQ_CENT,SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) SQ_CENT,
        0 BQ_DF,SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) SQ_DF,
        0 BQ_CITY,SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) SQ_CITY,
        0 BQ_COUNTY,SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) SQ_COUNTY,
        0 BQ_QX,SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) SQ_QX
        FROM ADS_TAX_INCOME_QX_MON A, DM_YSKM B,DM_XZQH C
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM1
        AND A.YSKM_DM = B.YSKM_DM
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC
        ) T
        GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC
        ORDER BY 2 ASC
    </select>
    <select id="selectSeparateAreaAndLevelCityDistrict" resultType="com.zjhh.tax.statistics.vo.SeparateAreaAndLevelVO">
        SELECT SJXZQH_DM ,XZQH_DM,XZQH_MC,
        SUM(BQ_YEARAMT) BQ_AMT,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END BQ_ZF,
        SUM(BQ_CENT) BQ_CENT,
        CASE WHEN SUM(SQ_CENT)=0 THEN NULL ELSE ROUND((SUM(BQ_CENT)/SUM(SQ_CENT)-1)*100,2) END CENT_ZF,
        SUM(BQ_DF) BQ_DF,
        CASE WHEN SUM(SQ_DF)=0 THEN NULL ELSE ROUND((SUM(BQ_DF)/SUM(SQ_DF)-1)*100,2) END DF_ZF,
        SUM(BQ_CITY) BQ_CITY,
        CASE WHEN SUM(SQ_CITY)=0 THEN NULL ELSE ROUND((SUM(BQ_CITY)/SUM(SQ_CITY)-1)*100,2) END CITY_ZF,
        SUM(BQ_COUNTY) BQ_COUNTY,
        CASE WHEN SUM(SQ_COUNTY)=0 THEN NULL ELSE ROUND((SUM(BQ_COUNTY)/SUM(SQ_COUNTY)-1)*100,2) END COUNTY_ZF,
        SUM(BQ_QX) BQ_QX,
        CASE WHEN SUM(SQ_QX)=0 THEN NULL ELSE ROUND((SUM(BQ_QX)/SUM(SQ_QX)-1)*100,2) END QX_ZF,
        CASE WHEN LENGTH(XZQH_DM) = 6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        -- 行政区划 1
        AND 1=1
        AND
        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        DM_YSKM AS (
        SELECT DISTINCT T.YSKM_DM
        FROM DM_GY_YSKM_ZQ T
        WHERE
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                T.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        AND T.SJYSKM_DM IN ('A','B')
        )
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        SUM(A.YEARAMT) BQ_YEARAMT,0 SQ_YEARAMT,
        SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) BQ_CENT,0 SQ_CENT,
        SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) BQ_DF,0 SQ_DF,
        SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) BQ_CITY,0 SQ_CITY,
        SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) BQ_COUNTY,0 SQ_COUNTY,
        SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) BQ_QX,0 SQ_QX
        FROM VW_ADS_TAX_INCOME_DAY_ALL A, DM_YSKM B,DM_XZQH C
        WHERE A.RQ = #{req.endDate}
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM1
        AND A.YSKM_DM = B.YSKM_DM
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC
        UNION ALL
        SELECT C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC,
        0 BQ_YEARAMT,SUM(A.YEARAMT) SQ_YEARAMT,
        0 BQ_CENT,SUM(CASE WHEN A.YSJC_DM='1' THEN A.YEARAMT ELSE 0 END) SQ_CENT,
        0 BQ_DF,SUM(CASE WHEN A.YSJC_DM&lt;>'1' THEN A.YEARAMT ELSE 0 END) SQ_DF,
        0 BQ_CITY,SUM(CASE WHEN A.YSJC_DM='2' THEN A.YEARAMT ELSE 0 END) SQ_CITY,
        0 BQ_COUNTY,SUM(CASE WHEN A.YSJC_DM IN('3') THEN A.YEARAMT ELSE 0 END) SQ_COUNTY,
        0 BQ_QX,SUM(CASE WHEN A.YSJC_DM IN('4','5') THEN A.YEARAMT ELSE 0 END) SQ_QX
        FROM VW_ADS_TAX_INCOME_MON_ALL A, DM_YSKM B,DM_XZQH C
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.XZQH_DM = C.XZQH_DM1
        AND A.YSKM_DM = B.YSKM_DM
        GROUP BY C.SJXZQH_DM,C.XZQH_DM,C.XZQH_MC
        ) T
        GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC
        ORDER BY 2 ASC
    </select>
</mapper>