<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.tax.statistics.dao.mapper.SeparateAreaPerformMapper">
    <cache eviction="LRU" type="com.zjhh.user.config.MybatisRedisCache"/>
    <select id="selectSeparateAreaPerformProvince" resultType="com.zjhh.tax.statistics.vo.SeparateAreaPerformVO">
        SELECT SJXZQH_DM ,XZQH_DM, XZQH_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        CASE WHEN SUM(SY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SY_MONTHAMT)-1)*100,2) END SY_ZF,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END SQ_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEARAMT) BQ_YEARAMT,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND(SUM(BQ_YEARAMT)/SUM(YSSAMT)*100,2) END WCJD,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END SN_ZF,
        CASE WHEN SUM(SSY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(SY_MONTHAMT)/SUM(SSY_MONTHAMT)-1)*100,2) END SSY_ZF,
        CASE WHEN SUM(SSQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(SQ_YEARAMT)/SUM(SSQ_YEARAMT)-1)*100,2) END SSQ_ZF,
        SUM(SY_MONTHAMT) SY_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(SSY_YEARAMT) SSY_MONTHAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        SUM(SY_YEARAMT) SY_YEARAMT,
        SUM(BQ_MONTHAMT)-SUM(SQ_MONTHAMT) SQ_MONTH_CE,
        SUM(BQ_YEARAMT)-SUM(SQ_YEARAMT) SQ_YEAR_CE,
        SUM(BQ_MONTHAMT)-SUM(SY_MONTHAMT) SY_MONTH_CE,
        CASE WHEN LENGTH(XZQH_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND B.XZQH_DM IN (SELECT XZQH_DM FROM DM_CZ_XZQH A WHERE A.SQBZ = '1')
        AND
        <choose>
            <when test="req.xzqh != null and req.xzqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="req.province != null and req.province == '1'.toString()">
                        ( A.SJXZQH_DM IS NULL)
                    </when>
                    <otherwise>
                        (
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.SJXZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                        OR
                        <choose>
                            <when test="req.ssqh != null and req.ssqh.size > 0">
                                A.XZQH_DM IN
                                <foreach collection="req.ssqh" index="index" item="item" open="(" separator=","
                                         close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                1=1
                            </otherwise>
                        </choose>
                    </otherwise>
                </choose>

            </otherwise>
        </choose>
        ),
        DM_YSKM AS (
        SELECT A.*,B.YSKM_DM AS CODE
        FROM DM_GY_PAGE_STYLE A,DM_GY_YSKM_ZQ B
        WHERE A.TYPE_DM IN ('1','2')
        AND A.DMMX = B.SJYSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                B.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>

        ),
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT, SUM(A.YEARAMT) BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_DAY A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = #{req.endDate}
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SY_MONTHAMT, SUM(A.YEARAMT) SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-1),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT, SUM(A.YEARAMT) SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        SUM(A.MONTHAMT) SSY_MONTHAMT, SUM(A.YEARAMT) SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-13),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        SUM(A.YEARAMT) SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-24),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A ,DM_XZQH D,
        (SELECT DISTINCT ZS_DM,ZS_MC,XH FROM DM_YSKM ) E
        WHERE A.XZQH_DM = D.XZQH_DM1
        AND A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.SJYSKM_DM = E.ZS_DM
        AND TYPE_CODE = '0'
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC

        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC
        ORDER BY 1 NULLS FIRST,2 ASC
    </select>
    <select id="selectSeparateAreaPerformDistrict" resultType="com.zjhh.tax.statistics.vo.SeparateAreaPerformVO">
        SELECT SJXZQH_DM ,XZQH_DM, XZQH_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        CASE WHEN SUM(SY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SY_MONTHAMT)-1)*100,2) END SY_ZF,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END SQ_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEARAMT) BQ_YEARAMT,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND(SUM(BQ_YEARAMT)/SUM(YSSAMT)*100,2) END WCJD,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END SN_ZF,
        CASE WHEN SUM(SSY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(SY_MONTHAMT)/SUM(SSY_MONTHAMT)-1)*100,2) END SSY_ZF,
        CASE WHEN SUM(SSQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(SQ_YEARAMT)/SUM(SSQ_YEARAMT)-1)*100,2) END SSQ_ZF,
        SUM(SY_MONTHAMT) SY_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(SSY_YEARAMT) SSY_MONTHAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        SUM(SY_YEARAMT) SY_YEARAMT,
        SUM(BQ_MONTHAMT)-SUM(SQ_MONTHAMT) SQ_MONTH_CE,
        SUM(BQ_YEARAMT)-SUM(SQ_YEARAMT) SQ_YEAR_CE,
        SUM(BQ_MONTHAMT)-SUM(SY_MONTHAMT) SY_MONTH_CE,
        CASE WHEN LENGTH(XZQH_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND
        (
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.SJXZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        OR
        <choose>
            <when test="req.ssqh != null and req.ssqh.size > 0">
                A.XZQH_DM IN
                <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        )
        ),
        DM_YSKM AS (
        SELECT A.*,B.YSKM_DM AS CODE
        FROM DM_GY_PAGE_STYLE A,DM_GY_YSKM_ZQ B
        WHERE A.TYPE_DM IN ('1','2')
        AND A.DMMX = B.SJYSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                B.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>

        ),
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT, SUM(A.YEARAMT) BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_QX_DAY A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = #{req.endDate}
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SY_MONTHAMT, SUM(A.YEARAMT) SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-1),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT, SUM(A.YEARAMT) SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        SUM(A.MONTHAMT) SSY_MONTHAMT, SUM(A.YEARAMT) SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-13),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        SUM(A.YEARAMT) SSQ_YEARAMT, 0 YSSAMT
        FROM ADS_TAX_INCOME_QX_MON A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-24),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A ,DM_XZQH D,
        (SELECT DISTINCT ZS_DM,ZS_MC,XH FROM DM_YSKM ) E
        WHERE A.XZQH_DM = D.XZQH_DM1
        AND A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.SJYSKM_DM = E.ZS_DM
        AND TYPE_CODE = '1'
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC

        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC
        ORDER BY 1 NULLS FIRST,2 ASC
    </select>
    <select id="selectSeparateAreaPerformCityDistrict" resultType="com.zjhh.tax.statistics.vo.SeparateAreaPerformVO">
        SELECT SJXZQH_DM ,XZQH_DM, XZQH_MC,
        SUM(BQ_MONTHAMT) BQ_MONTHAMT,
        CASE WHEN SUM(SY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SY_MONTHAMT)-1)*100,2) END SY_ZF,
        CASE WHEN SUM(SQ_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_MONTHAMT)/SUM(SQ_MONTHAMT)-1)*100,2) END SQ_ZF,
        SUM(YSSAMT) YSSAMT, SUM(BQ_YEARAMT) BQ_YEARAMT,
        CASE WHEN SUM(YSSAMT)=0 THEN NULL ELSE ROUND(SUM(BQ_YEARAMT)/SUM(YSSAMT)*100,2) END WCJD,
        CASE WHEN SUM(SQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(BQ_YEARAMT)/SUM(SQ_YEARAMT)-1)*100,2) END SN_ZF,
        CASE WHEN SUM(SSY_MONTHAMT)=0 THEN NULL ELSE ROUND((SUM(SY_MONTHAMT)/SUM(SSY_MONTHAMT)-1)*100,2) END SSY_ZF,
        CASE WHEN SUM(SSQ_YEARAMT)=0 THEN NULL ELSE ROUND((SUM(SQ_YEARAMT)/SUM(SSQ_YEARAMT)-1)*100,2) END SSQ_ZF,
        SUM(SY_MONTHAMT) SY_MONTHAMT,
        SUM(SQ_MONTHAMT) SQ_MONTHAMT,
        SUM(SSY_YEARAMT) SSY_MONTHAMT,
        SUM(SQ_YEARAMT) SQ_YEARAMT,
        SUM(SY_YEARAMT) SY_YEARAMT,
        SUM(BQ_MONTHAMT)-SUM(SQ_MONTHAMT) SQ_MONTH_CE,
        SUM(BQ_YEARAMT)-SUM(SQ_YEARAMT) SQ_YEAR_CE,
        SUM(BQ_MONTHAMT)-SUM(SY_MONTHAMT) SY_MONTH_CE,
        CASE WHEN LENGTH(XZQH_DM)>=6 THEN 1 ELSE 0 END ISLEAF
        FROM (
        WITH DM_XZQH AS (
        SELECT A.SJXZQH_DM,A.SJXZQH_MC,A.XZQH_DM,A.XZQH_MC,B.XZQH_DM XZQH_DM1
        FROM DM_CZ_XZQH A ,DM_CZ_XZQH_ZQ B
        WHERE A.XZQH_DM = B.SJXZQH_DM
        AND 1=1
        AND

        SUBSTR(A.XZQH_DM,1,4) IN
        <foreach collection="req.xzqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND
        A.XZQH_DM IN
        <foreach collection="req.ssqh" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>


        ),
        DM_YSKM AS (
        SELECT A.*,B.YSKM_DM AS CODE
        FROM DM_GY_PAGE_STYLE A,DM_GY_YSKM_ZQ B
        WHERE A.TYPE_DM IN ('1','2')
        AND A.DMMX = B.SJYSKM_DM
        AND
        <choose>
            <when test="req.yskm != null and req.yskm.size > 0">
                B.YSKM_DM IN
                <foreach collection="req.yskm" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>

        ),
        DM_YSJC AS (SELECT * FROM DM_GY_PAGE_STYLE T WHERE T.TYPE_DM = '0')
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        SUM(A.MONTHAMT) BQ_MONTHAMT, SUM(A.YEARAMT) BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM VW_ADS_TAX_INCOME_DAY_ALL A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = #{req.endDate}
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        SUM(A.MONTHAMT) SY_MONTHAMT, SUM(A.YEARAMT) SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-1),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        SUM(A.MONTHAMT) SQ_MONTHAMT, SUM(A.YEARAMT) SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-12),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        SUM(A.MONTHAMT) SSY_MONTHAMT, SUM(A.YEARAMT) SSY_YEARAMT,
        0 SSQ_YEARAMT, 0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-13),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        SUM(A.YEARAMT) SSQ_YEARAMT, 0 YSSAMT
        FROM VW_ADS_TAX_INCOME_MON_ALL A,DM_YSJC B ,DM_XZQH D,(SELECT DISTINCT CODE FROM DM_YSKM) E
        WHERE A.RQ = TO_CHAR(ADD_MONTHS(TO_DATE(#{req.endDate},'YYYYMMDD'),-24),'YYYYMM')
        AND CASE WHEN A.YSJC_DM = '2' THEN '330000' ELSE A.XZQH_DM END = D.XZQH_DM1
        AND A.XZQH_DM = B.MID_DM
        AND INSTR(B.DMMX,A.YSJC_DM) > 0
        AND
        <choose>
            <when test="req.zsjg != null and req.zsjg.size > 0">
                A.ZSJG_DM IN
                <foreach collection="req.zsjg" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                A.ZSJG_DM = '000000000000'
            </otherwise>
        </choose>
        AND A.YSKM_DM=E.CODE
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC
        UNION ALL
        SELECT D.SJXZQH_DM, D.XZQH_DM, D.XZQH_MC,
        0 BQ_MONTHAMT, 0 BQ_YEARAMT,
        0 SY_MONTHAMT, 0 SY_YEARAMT,
        0 SQ_MONTHAMT, 0 SQ_YEARAMT,
        0 SSY_MONTHAMT, 0 SSY_YEARAMT,
        0 SSQ_YEARAMT, SUM(A.YSSAMT) YSSAMT
        FROM ADS_TAX_INCOME_YSS_YEAR A ,DM_XZQH D,
        (SELECT DISTINCT ZS_DM,ZS_MC,XH FROM DM_YSKM ) E
        WHERE A.XZQH_DM = D.XZQH_DM1
        AND A.RQ = SUBSTR(#{req.endDate},1,4)
        AND A.SJYSKM_DM = E.ZS_DM
        AND TYPE_CODE IN('1','2')
        GROUP BY D.SJXZQH_DM,D.XZQH_DM,D.XZQH_MC

        ) T GROUP BY SJXZQH_DM,XZQH_DM,XZQH_MC
        ORDER BY 1 NULLS FIRST,2 ASC
    </select>
</mapper>