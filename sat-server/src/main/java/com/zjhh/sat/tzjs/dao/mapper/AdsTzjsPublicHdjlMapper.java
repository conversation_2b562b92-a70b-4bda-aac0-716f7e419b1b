package com.zjhh.sat.tzjs.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjhh.sat.tzjs.dao.entity.AdsTzjsPublicHdjl;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 体制结算核定结果表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@DS("tzjs")
@Mapper
public interface AdsTzjsPublicHdjlMapper extends CustomerBaseMapper<AdsTzjsPublicHdjl> {

    /**
     * 处理核定记录
     *
     * @param qyid
     * @param startDate
     * @param jdxzOld
     * @param jdxzNew
     * @param createUser
     * @param createXzqh
     */
    void callHdjlDeal(@Param("qyid") String qyid, @Param("startDate") String startDate, @Param("jdxzOld") String jdxzOld,
                      @Param("jdxzNew") String jdxzNew, @Param("createUser") String createUser, @Param("createXzqh") String createXzqh);

}
