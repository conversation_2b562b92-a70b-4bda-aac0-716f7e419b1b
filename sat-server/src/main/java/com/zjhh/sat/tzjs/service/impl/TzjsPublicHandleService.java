package com.zjhh.sat.tzjs.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.sat.tzjs.dao.entity.*;
import com.zjhh.sat.tzjs.dao.mapper.*;
import com.zjhh.sat.tzjs.enume.ProcessInit;
import com.zjhh.sat.tzjs.request.PageTzjsPublicReq;
import com.zjhh.sat.tzjs.request.TzjsPublicQyhdDetailReq;
import com.zjhh.user.vo.LoginVo;
import com.zjhh.workflow.service.FlowableHandler;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2025/4/23 16:19
 */
@Service
@Slf4j
public class TzjsPublicHandleService {

    @Resource
    private AdsTzjsPublicCheckMapper adsTzjsPublicCheckMapper;

    @Resource
    private AdsTzjsPublicCheckProcessMapper adsTzjsPublicCheckProcessMapper;

    @Resource
    private AdsTzjsPublicProcessInitMapper adsTzjsPublicProcessInitMapper;

    @Resource
    private AdsTzjsPublicNsrxxMapper adsTzjsPublicNsrxxMapper;

    @Resource
    private AdsTzjsPublicHdjlMapper adsTzjsPublicHdjlMapper;

    @Resource
    private DmTzjsPublicCzfpMapper dmTzjsPublicCzfpMapper;

    @Resource
    private FlowableHandler flowableHandler;

    @Resource
    private TaskService taskService;

    @Resource
    private AdsTzjsPublicChangeMapper adsTzjsPublicChangeMapper;

    @Resource
    private AdsTzjsPublicChangeProcessMapper adsTzjsPublicChangeProcessMapper;

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public void setPageTzjsPublic(PageTzjsPublicReq req, LoginVo loginVo, AdsTzjsPublicMenuProcess process) {
        String orgCode = loginVo.getDefaultAreaCode();
        String userCode = loginVo.getCode();
        String loginName = loginVo.getLoginName();
        List<String> roles = loginVo.getRoles();
        req.setOrgCode(orgCode);
        List<String> auths = new ArrayList<>();
        auths.add(orgCode);
        auths.add(loginName);
        auths.addAll(roles);
        if (req.getYwzt() == null) {
            // 将权限列表转换为逗号分隔的字符串
            String authString = String.join(",", auths);

            // 使用PostgreSQL的string_to_array函数和ANY操作符
            List<Task> tasks = taskService.createNativeTaskQuery()
                    .sql("select distinct T.* from ACT_RU_TASK T " +
                            "inner join ACT_RE_PROCDEF D on T.PROC_DEF_ID_ = D.ID_ " +
                            "left join ACT_RU_IDENTITYLINK L on L.TASK_ID_ = T.ID_ " +
                            "WHERE D.KEY_ = #{processKey} and T.SUSPENSION_STATE_ = 1 " +
                            "and (L.TYPE_ = 'candidate' and L.USER_ID_ = ANY(string_to_array(#{authString}, ',')))")
                    .parameter("processKey", process.getProcessKey())
                    .parameter("authString", authString)
                    .list();

            // 获取流程实例ID并去重
            List<String> processIds = tasks.stream()
                    .map(Task::getProcessInstanceId)
                    .distinct()
                    .toList();
            req.setProcessIds(processIds);
            req.setUserCode(userCode);
            req.setAuthAreaCodes(loginVo.getAuthAreaCodes());
        } else if (req.getYwzt() == 1) {
            // 将权限列表转换为逗号分隔的字符串
            String authString = String.join(",", auths);

            // 使用PostgreSQL的string_to_array函数和ANY操作符
            List<Task> tasks = taskService.createNativeTaskQuery()
                    .sql("select distinct T.* from ACT_RU_TASK T " +
                            "inner join ACT_RE_PROCDEF D on T.PROC_DEF_ID_ = D.ID_ " +
                            "left join ACT_RU_IDENTITYLINK L on L.TASK_ID_ = T.ID_ " +
                            "WHERE D.KEY_ = #{processKey} and T.SUSPENSION_STATE_ = 1 " +
                            "and (L.TYPE_ = 'candidate' and L.USER_ID_ = ANY(string_to_array(#{authString}, ',')))")
                    .parameter("processKey", process.getProcessKey())
                    .parameter("authString", authString)
                    .list();

            // 获取流程实例ID并去重
            List<String> processIds = tasks.stream()
                    .map(Task::getProcessInstanceId)
                    .distinct()
                    .toList();
            req.setProcessIds(processIds);
        } else if (req.getYwzt() == 9) {
            req.setUserCode(userCode);
            req.setAuthAreaCodes(loginVo.getAuthAreaCodes());
        } else {
            req.setUserCode(userCode);
        }
    }

    /**
     * 判断权限
     *
     * @param currentTaskId
     * @param loginVo
     */
    public void checkTaskAuth(String currentTaskId, LoginVo loginVo) {
        List<String> authCodes = flowableHandler.taskUsers(currentTaskId);
        if (!authCodes.contains(loginVo.getDefaultAreaCode()) && !authCodes.contains(loginVo.getLoginName()) &&
                Collections.disjoint(authCodes, loginVo.getRoles())) {
            throw new BizException("当前用户没有流程权限！");
        }
    }

    @DSTransactional(rollbackFor = Throwable.class)
    @Async
    public void qyhdSingleHandle(AdsTzjsPublicMenuProcess process, String dataOwner, TzjsPublicQyhdDetailReq item,
                                 LoginVo loginVo, LocalDateTime now, AtomicInteger successCount,
                                 AtomicInteger failCount, CountDownLatch countDownLatch) {
        try {
            doQyhdSingleHandle(process, dataOwner, item, loginVo, now);
            successCount.incrementAndGet();
            log.info("企业核定处理成功，企业ID: {}", item.getQyid());
        } catch (Exception e) {
            failCount.incrementAndGet();
            log.error("企业核定处理失败，企业ID: {}，错误信息: {}", item.getQyid(), e.getMessage(), e);
            throw new BizException(e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

    @DSTransactional(rollbackFor = Throwable.class)
    @Async
    public void changeSingleHandle(AdsTzjsPublicMenuProcess process, String changeId, LoginVo loginVo,
                                   LocalDateTime now, AtomicInteger successCount, AtomicInteger failCount, CountDownLatch countDownLatch) {
        try {
            doChangeSingleHandle(process, changeId, loginVo, now);
            successCount.incrementAndGet();
            log.info("企业调整处理成功，调整单ID: {}", changeId);
        } catch (Exception e) {
            failCount.incrementAndGet();
            log.error("企业调整处理失败，调整单ID: {}，错误信息: {}", changeId, e.getMessage(), e);
            throw new BizException(e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * 实际执行调整
     *
     * @param process
     * @param changeId
     * @param now
     */
    private void doChangeSingleHandle(AdsTzjsPublicMenuProcess process, String changeId, LoginVo loginVo, LocalDateTime now) {
        AdsTzjsPublicChange change = adsTzjsPublicChangeMapper.selectById(changeId);
        if (ObjectUtil.isNull(change)) {
            throw new BizException("该调整单不存在！");
        }
        String qyid = change.getQyid();
        String dataOwner = change.getDataOwner();
        if (change.getStatus() == 9) {
            throw new BizException("该调整单已结束！");
        }
        String orgCode = loginVo.getDefaultAreaCode();
        String userCode = loginVo.getCode();

        if (change.getStatus() == 0) {
            // 未启动流程
            QueryWrapper<AdsTzjsPublicProcessInit> processInitWrapper = new QueryWrapper<>();
            processInitWrapper.lambda().eq(AdsTzjsPublicProcessInit::getType, ProcessInit.QYTZ.getType())
                    .eq(AdsTzjsPublicProcessInit::getOrgCode, orgCode)
                    .eq(AdsTzjsPublicProcessInit::getDataOwner, dataOwner);
            if (!adsTzjsPublicProcessInitMapper.exists(processInitWrapper)) {
                throw new BizException("当前用户没有流程权限！");
            }

            Map<String, Object> map = BeanUtil.beanToMap(change);
            map.put("jdxzDm", change.getJdxzDmNew());
            map.put("jdxzDmNew", change.getJdxzDmNew());
            map.put("xzqhDmOld", getXzqhDmByJdxzDm(change.getJdxzDmOld()));
            map.put("xzqhDmNew", getXzqhDmByJdxzDm(change.getJdxzDmNew()));
            String processId = flowableHandler.startProcess(process.getProcessKey(), map, userCode);
            change.setProcessId(processId);

            change.setStatus(1);
            change.setUpdateTime(now);
            adsTzjsPublicChangeMapper.updateById(change);

            AdsTzjsPublicChangeProcess startProcess = new AdsTzjsPublicChangeProcess();
            startProcess.setId(IdUtil.getSnowflakeNextIdStr());
            startProcess.setProcessId(processId);
            startProcess.setNodeId(IdUtil.getSnowflakeNextIdStr());
            startProcess.setNodeKey("start");
            startProcess.setNodeName("开始");
            startProcess.setStatus(1);
            startProcess.setHandleTime(now);
            startProcess.setCreateTime(now);
            startProcess.setUpdateTime(now);
            startProcess.setDeleted(false);
            adsTzjsPublicChangeProcessMapper.insert(startProcess);

            // 开始节点同时操作两步
            Task currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (currentTask == null) {
                throw new BizException("流程已结束！");
            }

            AdsTzjsPublicChangeProcess nextProcess = new AdsTzjsPublicChangeProcess();
            nextProcess.setId(IdUtil.getSnowflakeNextIdStr());
            nextProcess.setProcessId(processId);
            nextProcess.setNodeId(currentTask.getId());
            nextProcess.setNodeKey(currentTask.getTaskDefinitionKey());
            nextProcess.setNodeName(currentTask.getName());
            nextProcess.setStartDate(change.getStartDate());
            nextProcess.setJdxzDm(change.getJdxzDmNew());
            nextProcess.setStatus(1);
            nextProcess.setAssignee(userCode);
            nextProcess.setHandleTime(now);
            nextProcess.setCreateTime(now);
            nextProcess.setUpdateTime(now);
            nextProcess.setDeleted(false);
            adsTzjsPublicChangeProcessMapper.insert(nextProcess);

            flowableHandler.claimTask(currentTask.getId(), userCode);
            flowableHandler.completeTask(processId, map, "通过", null, userCode);

            currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (currentTask != null) {
                addChangeProcessing(processId, currentTask, now);
            } else {
                // 流程结束
                endChangeProcess(processId, change, dataOwner, userCode, now);
            }
            UpdateWrapper<AdsTzjsPublicNsrxx> nsrxxWrapper = new UpdateWrapper<>();
            nsrxxWrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, qyid)
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, dataOwner)
                    .set(AdsTzjsPublicNsrxx::getHdFlag, 1);
            adsTzjsPublicNsrxxMapper.update(null, nsrxxWrapper);
        } else {
            String processId = change.getProcessId();
            Task currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (currentTask == null) {
                throw new BizException("流程已结束！");
            }

            // 判断工作流权限
            checkTaskAuth(currentTask.getId(), loginVo);


            Map<String, Object> var = BeanUtil.beanToMap(change);
            var.put("jdxzDm", change.getJdxzDmNew());
            var.put("jdxzDmNew", change.getJdxzDmNew());
            var.put("xzqhDmOld", getXzqhDmByJdxzDm(change.getJdxzDmOld()));
            var.put("xzqhDmNew", getXzqhDmByJdxzDm(change.getJdxzDmNew()));
            flowableHandler.claimTask(currentTask.getId(), userCode);
            flowableHandler.completeTask(processId, var, "通过", null, userCode);

            UpdateWrapper<AdsTzjsPublicChangeProcess> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(AdsTzjsPublicChangeProcess::getProcessId, processId)
                    .eq(AdsTzjsPublicChangeProcess::getNodeId, currentTask.getId())
                    .eq(AdsTzjsPublicChangeProcess::getStatus, 0)
                    .eq(AdsTzjsPublicChangeProcess::getDeleted, false)
                    .set(AdsTzjsPublicChangeProcess::getJdxzDm, change.getJdxzDmNew())
                    .set(AdsTzjsPublicChangeProcess::getStatus, 1)
                    .set(AdsTzjsPublicChangeProcess::getAssignee, userCode)
                    .set(AdsTzjsPublicChangeProcess::getHandleTime, now)
                    .set(AdsTzjsPublicChangeProcess::getUpdateTime, now);
            adsTzjsPublicChangeProcessMapper.update(null, updateWrapper);
            currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (ObjectUtil.isNotNull(currentTask)) {
                addChangeProcessing(processId, currentTask, now);
            } else {
                endChangeProcess(processId, change, dataOwner, userCode, now);
            }
        }
    }

    /**
     * 实际执行企业核定处理的业务逻辑
     * 该方法不捕获异常，让异常向上抛出以便外层方法处理
     */
    private void doQyhdSingleHandle(AdsTzjsPublicMenuProcess process, String dataOwner, TzjsPublicQyhdDetailReq item, LoginVo loginVo, LocalDateTime now) {
        QueryWrapper<AdsTzjsPublicCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsTzjsPublicCheck::getQyid, item.getQyid())
                .eq(AdsTzjsPublicCheck::getStatus, 0)
                .eq(AdsTzjsPublicCheck::getDataOwner, dataOwner);
        AdsTzjsPublicCheck check = adsTzjsPublicCheckMapper.selectOne(checkWrapper);

        String orgCode = loginVo.getDefaultAreaCode();
        String userCode = loginVo.getCode();

        if (check == null) {
            // 未启动核定
            QueryWrapper<AdsTzjsPublicProcessInit> processInitWrapper = new QueryWrapper<>();
            processInitWrapper.lambda().eq(AdsTzjsPublicProcessInit::getType, ProcessInit.QYHD.getType())
                    .eq(AdsTzjsPublicProcessInit::getOrgCode, orgCode)
                    .eq(AdsTzjsPublicProcessInit::getDataOwner, dataOwner);
            if (!adsTzjsPublicProcessInitMapper.exists(processInitWrapper)) {
                throw new BizException("当前用户没有流程权限！");
            }
            QueryWrapper<AdsTzjsPublicNsrxx> nsrxxQueryWrapper = new QueryWrapper<>();
            nsrxxQueryWrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, item.getQyid())
                    .eq(AdsTzjsPublicNsrxx::getHdFlag, 0)
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, dataOwner);
            if (!adsTzjsPublicNsrxxMapper.exists(nsrxxQueryWrapper)) {
                throw new BizException("当前企业状态无法启动核定流程");
            }

            check = new AdsTzjsPublicCheck();
            check.setId(IdUtil.getSnowflakeNextIdStr());
            check.setQyid(item.getQyid());
            check.setJdxzDm(item.getJdxzDm());
            check.setStatus(0);
            check.setDataOwner(dataOwner);
            check.setCreateUser(userCode);
            check.setCreateTime(now);
            check.setUpdateTime(now);

            Map<String, Object> map = BeanUtil.beanToMap(check);
            map.put("jdxzDm", item.getJdxzDm());
            map.put("xzqhDm", getXzqhDmByJdxzDm(item.getJdxzDm()));
            String processId = flowableHandler.startProcess(process.getProcessKey(), map, userCode);
            check.setProcessId(processId);

            adsTzjsPublicCheckMapper.insert(check);

            AdsTzjsPublicCheckProcess startProcess = new AdsTzjsPublicCheckProcess();
            startProcess.setId(IdUtil.getSnowflakeNextIdStr());
            startProcess.setProcessId(processId);
            startProcess.setNodeId(IdUtil.getSnowflakeNextIdStr());
            startProcess.setNodeKey("start");
            startProcess.setNodeName("开始");
            startProcess.setStatus(1);
            startProcess.setHandleTime(now);
            startProcess.setCreateTime(now);
            startProcess.setUpdateTime(now);
            startProcess.setDeleted(false);
            adsTzjsPublicCheckProcessMapper.insert(startProcess);

            // 开始节点同时操作两步
            Task currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (currentTask == null) {
                throw new BizException("流程已结束！");
            }
            AdsTzjsPublicCheckProcess nextProcess = new AdsTzjsPublicCheckProcess();
            nextProcess.setId(IdUtil.getSnowflakeNextIdStr());
            nextProcess.setProcessId(processId);
            nextProcess.setNodeId(currentTask.getId());
            nextProcess.setNodeKey(currentTask.getTaskDefinitionKey());
            nextProcess.setNodeName(currentTask.getName());
            nextProcess.setJdxzDm(item.getJdxzDm());
            nextProcess.setStatus(1);
            nextProcess.setAssignee(userCode);
            nextProcess.setHandleTime(now);
            nextProcess.setCreateTime(now);
            nextProcess.setUpdateTime(now);
            nextProcess.setDeleted(false);
            adsTzjsPublicCheckProcessMapper.insert(nextProcess);


            flowableHandler.claimTask(currentTask.getId(), userCode);
            flowableHandler.completeTask(processId, map, "通过", null, userCode);

            currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (currentTask != null) {
                addQyhdProcessing(processId, currentTask, now);
            } else {
                // 流程结束
                endQyhdProcess(processId, check, dataOwner, item, userCode, now);
            }
            UpdateWrapper<AdsTzjsPublicNsrxx> nsrxxWrapper = new UpdateWrapper<>();
            nsrxxWrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, item.getQyid())
                    .eq(AdsTzjsPublicNsrxx::getDataOwner, dataOwner)
                    .set(AdsTzjsPublicNsrxx::getHdFlag, 1);
            adsTzjsPublicNsrxxMapper.update(null, nsrxxWrapper);
        } else {
            // 已启动核定流程
            String processId = check.getProcessId();
            Task currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (currentTask == null) {
                throw new BizException("流程已结束！");
            }

            // 判断工作流权限
            checkTaskAuth(currentTask.getId(), loginVo);

            Map<String, Object> var = BeanUtil.beanToMap(check);
            var.put("jdxzDm", item.getJdxzDm());
            var.put("xzqhDm", getXzqhDmByJdxzDm(item.getJdxzDm()));
            flowableHandler.claimTask(currentTask.getId(), userCode);
            flowableHandler.completeTask(processId, var, "通过", null, userCode);

            UpdateWrapper<AdsTzjsPublicCheckProcess> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(AdsTzjsPublicCheckProcess::getProcessId, processId)
                    .eq(AdsTzjsPublicCheckProcess::getNodeId, currentTask.getId())
                    .eq(AdsTzjsPublicCheckProcess::getStatus, 0)
                    .eq(AdsTzjsPublicCheckProcess::getDeleted, false)
                    .set(AdsTzjsPublicCheckProcess::getJdxzDm, item.getJdxzDm())
                    .set(AdsTzjsPublicCheckProcess::getStatus, 1)
                    .set(AdsTzjsPublicCheckProcess::getAssignee, userCode)
                    .set(AdsTzjsPublicCheckProcess::getHandleTime, now)
                    .set(AdsTzjsPublicCheckProcess::getUpdateTime, now);
            adsTzjsPublicCheckProcessMapper.update(null, updateWrapper);
            currentTask = flowableHandler.getCurrentTaskByProcessId(processId);
            if (ObjectUtil.isNotNull(currentTask)) {
                addQyhdProcessing(processId, currentTask, now);
            } else {
                endQyhdProcess(processId, check, dataOwner, item, userCode, now);
            }
        }
    }

    /**
     * 流程结束
     *
     * @param processId
     * @param change
     * @param dataOwner
     * @param userCode
     * @param now
     */
    public void endChangeProcess(String processId, AdsTzjsPublicChange change, String dataOwner, String userCode,
                                 LocalDateTime now) {
        AdsTzjsPublicChangeProcess endProcess = new AdsTzjsPublicChangeProcess();
        endProcess.setId(IdUtil.getSnowflakeNextIdStr());
        endProcess.setProcessId(processId);
        endProcess.setNodeId(IdUtil.getSnowflakeNextIdStr());
        endProcess.setNodeKey("end");
        endProcess.setNodeName("结束");
        endProcess.setStatus(1);
        endProcess.setHandleTime(now);
        endProcess.setCreateTime(now);
        endProcess.setUpdateTime(now);
        endProcess.setDeleted(false);
        adsTzjsPublicChangeProcessMapper.insert(endProcess);

        // 流程结束
        UpdateWrapper<AdsTzjsPublicChange> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(AdsTzjsPublicChange::getId, change.getId())
                .set(AdsTzjsPublicChange::getStatus, 9)
                .set(AdsTzjsPublicChange::getJdxzDmNew, change.getJdxzDmNew())
                .set(AdsTzjsPublicChange::getUpdateTime, now);
        adsTzjsPublicChangeMapper.update(null, updateWrapper);

        // 插入核定记录
        adsTzjsPublicHdjlMapper.callHdjlDeal(change.getQyid(), change.getStartDate().format(DATE_TIME_FORMAT),
                change.getJdxzDmOld(), change.getJdxzDmNew(), userCode, dataOwner);

        QueryWrapper<DmTzjsPublicCzfp> czfpWrapper = new QueryWrapper<>();
        czfpWrapper.lambda().eq(DmTzjsPublicCzfp::getJdxzDm, change.getJdxzDmNew())
                .select(DmTzjsPublicCzfp::getXzqhDm);
        DmTzjsPublicCzfp dmTzjsPublicCzfp = dmTzjsPublicCzfpMapper.selectOne(czfpWrapper);
        if (dmTzjsPublicCzfp == null) {
            throw new BizException("行政区划代码不存在！");
        }

        UpdateWrapper<AdsTzjsPublicNsrxx> nsrxxWrapper = new UpdateWrapper<>();
        nsrxxWrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, change.getQyid())
                .eq(AdsTzjsPublicNsrxx::getDataOwner, dataOwner)
                .set(AdsTzjsPublicNsrxx::getXzqhDm, dmTzjsPublicCzfp.getXzqhDm())
                .set(AdsTzjsPublicNsrxx::getJdxzDm, change.getJdxzDmNew());
        adsTzjsPublicNsrxxMapper.update(null, nsrxxWrapper);
    }

    /**
     * 添加流程中节点
     *
     * @param processId
     * @param currentTask
     * @param now
     */
    public void addChangeProcessing(String processId, Task currentTask, LocalDateTime now) {
        AdsTzjsPublicChangeProcess nextProcess = new AdsTzjsPublicChangeProcess();
        nextProcess.setId(IdUtil.getSnowflakeNextIdStr());
        nextProcess.setProcessId(processId);
        nextProcess.setNodeId(currentTask.getId());
        nextProcess.setNodeKey(currentTask.getTaskDefinitionKey());
        nextProcess.setNodeName(currentTask.getName());
        nextProcess.setStatus(0);
        nextProcess.setCreateTime(now);
        nextProcess.setUpdateTime(now);
        nextProcess.setDeleted(false);
        adsTzjsPublicChangeProcessMapper.insert(nextProcess);
    }


    /**
     * 添加流程中节点
     *
     * @param processId
     * @param currentTask
     * @param now
     */
    public void addQyhdProcessing(String processId, Task currentTask, LocalDateTime now) {
        AdsTzjsPublicCheckProcess nextProcess = new AdsTzjsPublicCheckProcess();
        nextProcess.setId(IdUtil.getSnowflakeNextIdStr());
        nextProcess.setProcessId(processId);
        nextProcess.setNodeId(currentTask.getId());
        nextProcess.setNodeKey(currentTask.getTaskDefinitionKey());
        nextProcess.setNodeName(currentTask.getName());
        nextProcess.setStatus(0);
        nextProcess.setCreateTime(now);
        nextProcess.setUpdateTime(now);
        nextProcess.setDeleted(false);
        adsTzjsPublicCheckProcessMapper.insert(nextProcess);
    }

    /**
     * 流程结束
     *
     * @param processId
     * @param check
     * @param dataOwner
     * @param item
     * @param userCode
     * @param now
     */
    public void endQyhdProcess(String processId, AdsTzjsPublicCheck check, String dataOwner, TzjsPublicQyhdDetailReq item,
                               String userCode, LocalDateTime now) {
        AdsTzjsPublicCheckProcess endProcess = new AdsTzjsPublicCheckProcess();
        endProcess.setId(IdUtil.getSnowflakeNextIdStr());
        endProcess.setProcessId(processId);
        endProcess.setNodeId(IdUtil.getSnowflakeNextIdStr());
        endProcess.setNodeKey("end");
        endProcess.setNodeName("结束");
        endProcess.setStatus(1);
        endProcess.setHandleTime(now);
        endProcess.setCreateTime(now);
        endProcess.setUpdateTime(now);
        endProcess.setDeleted(false);
        adsTzjsPublicCheckProcessMapper.insert(endProcess);

        UpdateWrapper<AdsTzjsPublicCheck> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(AdsTzjsPublicCheck::getId, check.getId())
                .set(AdsTzjsPublicCheck::getStatus, 1)
                .set(AdsTzjsPublicCheck::getJdxzDm, item.getJdxzDm())
                .set(AdsTzjsPublicCheck::getUpdateTime, now);
        adsTzjsPublicCheckMapper.update(null, updateWrapper);

        // 插入核定记录
        adsTzjsPublicHdjlMapper.callHdjlDeal(item.getQyid(), null, null, item.getJdxzDm(), userCode, dataOwner);

        QueryWrapper<DmTzjsPublicCzfp> czfpWrapper = new QueryWrapper<>();
        czfpWrapper.lambda().eq(DmTzjsPublicCzfp::getJdxzDm, item.getJdxzDm())
                .select(DmTzjsPublicCzfp::getXzqhDm);
        DmTzjsPublicCzfp dmTzjsPublicCzfp = dmTzjsPublicCzfpMapper.selectOne(czfpWrapper);
        if (dmTzjsPublicCzfp == null) {
            throw new BizException("行政区划代码不存在！");
        }
        UpdateWrapper<AdsTzjsPublicNsrxx> nsrxxWrapper = new UpdateWrapper<>();
        nsrxxWrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, check.getQyid())
                .eq(AdsTzjsPublicNsrxx::getDataOwner, dataOwner)
                .set(AdsTzjsPublicNsrxx::getXzqhDm, dmTzjsPublicCzfp.getXzqhDm())
                .set(AdsTzjsPublicNsrxx::getJdxzDm, item.getJdxzDm());
        adsTzjsPublicNsrxxMapper.update(null, nsrxxWrapper);
    }

    /**
     * 获取行政区划代码
     *
     * @param jdxzDm
     * @return
     */
    private String getXzqhDmByJdxzDm(String jdxzDm) {
        QueryWrapper<DmTzjsPublicCzfp> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DmTzjsPublicCzfp::getJdxzDm, jdxzDm)
                .select(DmTzjsPublicCzfp::getXzqhDm);
        DmTzjsPublicCzfp dmTzjsPublicCzfp = dmTzjsPublicCzfpMapper.selectOne(wrapper);
        if (dmTzjsPublicCzfp == null) {
            return null;
        }
        return dmTzjsPublicCzfp.getXzqhDm();
    }
}
