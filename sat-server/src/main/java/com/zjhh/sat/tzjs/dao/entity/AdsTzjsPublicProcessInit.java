package com.zjhh.sat.tzjs.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_tzjs_public_process_init")
public class AdsTzjsPublicProcessInit implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 类型：1-企业核定 2-核定调整
     */
    private Integer type;

    /**
     * 数据标识
     */
    private String dataOwner;

    /**
     * 对应用户的组织机构代码
     */
    private String orgCode;


}
