package com.zjhh.sat.jxsrcxfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.cz.req.IdsStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.constants.JxTzjsConstant;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedBatch;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedCheck;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedNsrxx;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedBatchMapper;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedCheckMapper;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedNsrxxMapper;
import com.zjhh.sat.jxsrcxfx.dto.CheckNsrxxDto;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.PageDistrictCheckStateReq;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.UpdateCzfpReq;
import com.zjhh.sat.jxsrcxfx.service.DistrictCheckStateService;
import com.zjhh.sat.jxsrcxfx.utils.MsgUtils;
import com.zjhh.sat.jxsrcxfx.vo.districtcheckstate.DistrictCheckStateVo;
import com.zjhh.sat.tzjs.dao.entity.AdsTzjsPublicNsrxx;
import com.zjhh.sat.tzjs.dao.mapper.AdsTzjsPublicHdjlMapper;
import com.zjhh.sat.tzjs.dao.mapper.AdsTzjsPublicNsrxxMapper;
import com.zjhh.user.service.impl.UserSession;
import com.zjhh.user.vo.LoginVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/15 17:27
 */
@Slf4j
@Service
@DS("tzjs")
public class DistrictCheckStateServiceImpl implements DistrictCheckStateService {

    @Resource
    private AdsSatIssuedNsrxxMapper adsSatIssuedNsrxxMapper;

    @Resource
    private AdsSatIssuedBatchMapper adsSatIssuedBatchMapper;

    @Resource
    private AdsSatIssuedCheckMapper adsSatIssuedCheckMapper;

    @Resource
    private AdsTzjsPublicNsrxxMapper adsTzjsPublicNsrxxMapper;

    @Resource
    private AdsTzjsPublicHdjlMapper adsTzjsPublicHdjlMapper;

    @Resource
    private UserSession userSession;

    @Override
    public Page<DistrictCheckStateVo> pageDistrictCheckState(PageDistrictCheckStateReq req) {
        return adsSatIssuedNsrxxMapper.pageDistrictCheckState(req.getPage(DistrictCheckStateVo.class), req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateCzfp(UpdateCzfpReq req) {
        validateCheck(req.getIds());
        List<AdsSatIssuedNsrxx> nsrxxes = adsSatIssuedNsrxxMapper.selectByIds(req.getIds());
        List<String> batchNos = new ArrayList<>();
        nsrxxes.forEach(nsrxx -> batchNos.add(nsrxx.getBatchNo()));
        validateDistrictIssued(batchNos);
        adsSatIssuedCheckMapper.deleteBatch(nsrxxes);
        List<AdsSatIssuedCheck> checks = new ArrayList<>();
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        nsrxxes.forEach(nsrxx -> {
            AdsSatIssuedCheck check = new AdsSatIssuedCheck();
            check.setId(IdUtil.getSnowflakeNextIdStr());
            check.setQyid(nsrxx.getQyid());
            check.setBatchNo(nsrxx.getBatchNo());
            check.setCheckType(5);
            check.setXzqhDm(req.getXzqhDm());
            check.setJdxzDm(req.getJdxzDm());
            check.setCreateUser(userCode);
            check.setCreateTime(now);
            checks.add(check);
        });
        adsSatIssuedCheckMapper.insertBatchSomeColumn(checks);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateCheck(IdsStringReq req) {
        validateCheck(req.getIds());
        List<CheckNsrxxDto> nsrxxDtos = adsSatIssuedNsrxxMapper.listCheckNsrxx(req.getIds());
        List<String> batchNos = new ArrayList<>();
        nsrxxDtos.forEach(nsrxx -> batchNos.add(nsrxx.getBatchNo()));

        validateDistrictIssued(batchNos);

        List<String> nsrmcs = adsSatIssuedNsrxxMapper.listManyCheck(req.getIds());
        MsgUtils.checkOperate(nsrmcs);

        String userCode = userSession.getUserCode();

        nsrxxDtos.forEach(dto -> {
            AdsSatIssuedNsrxx entity = new AdsSatIssuedNsrxx();
            entity.setId(dto.getId());
            if (StrUtil.isBlank(dto.getXzqhDm()) || StrUtil.isBlank(dto.getJdxzDm())) {
                entity.setStatus(2);
                adsSatIssuedNsrxxMapper.updateById(entity);
            } else {
                entity.setStatus(1);
                adsSatIssuedNsrxxMapper.updateById(entity);

                // 修改核定结果
                UpdateWrapper<AdsTzjsPublicNsrxx> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(AdsTzjsPublicNsrxx::getQyid, dto.getId())
                        .eq(AdsTzjsPublicNsrxx::getDataOwner, JxTzjsConstant.DATA_OWNER)
                        .set(AdsTzjsPublicNsrxx::getXzqhDm, dto.getXzqhDm())
                        .set(AdsTzjsPublicNsrxx::getJdxzDm, dto.getJdxzDm());
                adsTzjsPublicNsrxxMapper.update(updateWrapper);

                // 执行核定存储过程
                adsTzjsPublicHdjlMapper.callHdjlDeal(dto.getQyid(), null, null, dto.getJdxzDm(), userCode, JxTzjsConstant.DATA_OWNER);
            }
        });
    }

    @Override
    public void updateCancel(IdsStringReq req) {
        validateCheck(req.getIds());
        List<AdsSatIssuedNsrxx> nsrxxes = adsSatIssuedNsrxxMapper.selectByIds(req.getIds());
        List<String> batchNos = new ArrayList<>();
        nsrxxes.forEach(nsrxx -> batchNos.add(nsrxx.getBatchNo()));
        validateDistrictIssued(batchNos);
        AdsSatIssuedNsrxx updateNsrxx = new AdsSatIssuedNsrxx();
        updateNsrxx.setStatus(3);
        QueryWrapper<AdsSatIssuedNsrxx> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(AdsSatIssuedNsrxx::getId, req.getIds());
        adsSatIssuedNsrxxMapper.update(updateNsrxx, wrapper);
    }

    private void validateCheck(List<String> nsrxxIds) {
        QueryWrapper<AdsSatIssuedNsrxx> nsrxxWrapper = new QueryWrapper<>();
        nsrxxWrapper.lambda().in(AdsSatIssuedNsrxx::getId, nsrxxIds)
                .in(AdsSatIssuedNsrxx::getStatus, CollUtil.list(false, 1, 2, 3));
        if (adsSatIssuedNsrxxMapper.selectCount(nsrxxWrapper) > 0) {
            throw new BizException("财政已确认，不可财政分片！");
        }
    }

    private void validateDistrictIssued(List<String> batchNos) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getReported, false)
                .in(AdsSatIssuedBatch::getBatchNo, batchNos);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("区级未全部提交，不可财政分片！");
        }
    }
}
