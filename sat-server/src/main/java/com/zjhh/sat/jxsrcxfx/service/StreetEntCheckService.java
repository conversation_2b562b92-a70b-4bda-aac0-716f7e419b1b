package com.zjhh.sat.jxsrcxfx.service;

import com.zjhh.cz.req.IdStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.ListBatchStreetEntReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.PageStreetCheckIssuedReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.PageStreetEntCheckReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.UpdateCzfpReq;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.BatchStreetEntVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetCheckIssuedVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetEntCheckVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetStatusVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/16 14:08
 */
public interface StreetEntCheckService {

    /**
     * 街道企业核定批次-列表
     *
     * @param req
     * @return
     */
    Page<StreetEntCheckVo> pageStreetEntCheck(PageStreetEntCheckReq req);

    /**
     * 企业核定下发表-列表
     *
     * @param req
     * @return
     */
    Page<StreetCheckIssuedVo> pageStreetCheckIssued(PageStreetCheckIssuedReq req);

    /**
     * 财政分片
     *
     * @param req
     */
    void updateCzfp(UpdateCzfpReq req);

    /**
     * 撤销分片
     *
     * @param req
     */
    void updateRevokeCzfp(UpdateCzfpReq req);

    /**
     * 确认上报
     *
     * @param req
     */
    void UpdateReported(IdStringReq req);

    /**
     * 撤销上报
     *
     * @param req
     */
    void updateRevokeReported(IdStringReq req);

    /**
     * 导出所有批次街道
     *
     * @param req
     * @return
     */
    List<BatchStreetEntVo> listBatchStreetEnt(ListBatchStreetEntReq req);

    /**
     * 获取企业核定下发表批次状态
     *
     * @param req
     * @return
     */
    StreetStatusVo getStreetStatus(IdStringReq req);
}
