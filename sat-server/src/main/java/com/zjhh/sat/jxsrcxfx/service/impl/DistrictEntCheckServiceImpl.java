package com.zjhh.sat.jxsrcxfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.cz.req.IdStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedBatch;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedCheck;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedNsrxx;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedBatchMapper;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedCheckMapper;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedNsrxxMapper;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.*;
import com.zjhh.sat.jxsrcxfx.service.DistrictEntCheckService;
import com.zjhh.sat.jxsrcxfx.utils.BatchNoUtil;
import com.zjhh.sat.jxsrcxfx.utils.MsgUtils;
import com.zjhh.sat.jxsrcxfx.vo.districtentcheck.*;
import com.zjhh.user.service.impl.UserSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/2/15 15:38
 */
@Slf4j
@Service
@DS("tzjs")
public class DistrictEntCheckServiceImpl implements DistrictEntCheckService {

    @Resource
    private AdsSatIssuedBatchMapper adsSatIssuedBatchMapper;

    @Resource
    private AdsSatIssuedNsrxxMapper adsSatIssuedNsrxxMapper;

    @Resource
    private AdsSatIssuedCheckMapper adsSatIssuedCheckMapper;

    @Resource
    private UserSession userSession;

    @Override
    public Page<DistrictIssuedBatchVo> pageDistrictIssuedBatch(PageDistrictEntIssuedBatchReq req) {
        req.setAreaCode(userSession.getDefaultAreaCode());
        req.setReported(req.getCommitType() == 1);
        return adsSatIssuedBatchMapper.pageDistrictIssuedBatch(req.getPage(DistrictIssuedBatchVo.class), req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateDistrictIssued(UpdateDistrictIssuedReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        if (adsSatIssuedBatch.getReported()) {
            throw new BizException("该批次已上报，无法下发！");
        }
        Set<String> nsrmcs = adsSatIssuedNsrxxMapper.listDistrictIssued(req.getParentId(), req.getQyids());
        if (nsrmcs.size() < 3) {
            nsrmcs.addAll(adsSatIssuedNsrxxMapper.listDistrictCheck(req.getParentId(), req.getQyids(), adsSatIssuedBatch.getAreaCode(), true));
        }
        MsgUtils.checkOperate(nsrmcs, "不可下发！");
        List<AdsSatIssuedBatch> batches = new ArrayList<>();
        List<AdsSatIssuedNsrxx> nsrxxes = new ArrayList<>();
        String batchNo = BatchNoUtil.crateBatchNo();
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        req.getXzqhDms().forEach(xzqhDm -> {
            AdsSatIssuedBatch batch = new AdsSatIssuedBatch();
            batch.setId(IdUtil.getSnowflakeNextIdStr());
            batch.setParentId(req.getParentId());
            batch.setBatchNo(batchNo);
            batch.setAreaCode(xzqhDm);
            batch.setReported(false);
            batch.setConfirmed(false);
            batch.setCreateUser(userCode);
            batch.setCreateTime(now);
            batches.add(batch);
        });
        adsSatIssuedBatchMapper.insertBatchSomeColumn(batches);
        req.getQyids().forEach(qyid -> {
            AdsSatIssuedNsrxx nsrxx = new AdsSatIssuedNsrxx();
            nsrxx.setId(IdUtil.getSnowflakeNextIdStr());
            nsrxx.setQyid(qyid);
            nsrxx.setBatchNo(batchNo);
            // 子批次类型为2
            nsrxx.setBatchType(2);
            nsrxx.setStatus(0);
            nsrxx.setCreateUser(userCode);
            nsrxx.setCreateTime(now);
            nsrxxes.add(nsrxx);
        });
        adsSatIssuedNsrxxMapper.insertBatchSomeColumn(nsrxxes);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateDistrictCzfp(UpdateDistrictCzfpReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        if (adsSatIssuedBatch.getReported()) {
            throw new BizException("该批次已上报，无法做财政分片！");
        }
        Set<String> nsrmcs = adsSatIssuedNsrxxMapper.listDistrictIssued(req.getParentId(), req.getQyids());
        MsgUtils.checkOperate(nsrmcs, "不可进行财政分片！");
        String areaCode = userSession.getDefaultAreaCode();
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, adsSatIssuedBatch.getBatchNo())
                .in(AdsSatIssuedCheck::getQyid, req.getQyids())
                .eq(AdsSatIssuedCheck::getXzqhDm, areaCode)
                .eq(AdsSatIssuedCheck::getCheckType, 3);
        adsSatIssuedCheckMapper.delete(checkWrapper);
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        List<AdsSatIssuedCheck> checks = new ArrayList<>();
        req.getQyids().forEach(qyid -> {
            AdsSatIssuedCheck check = new AdsSatIssuedCheck();
            check.setId(IdUtil.getSnowflakeNextIdStr());
            check.setQyid(qyid);
            check.setXzqhDm(areaCode);
            check.setJdxzDm(req.getJdxzDm());
            check.setBatchNo(adsSatIssuedBatch.getBatchNo());
            check.setCheckType(3);
            check.setCreateUser(userCode);
            check.setCreateTime(now);
            checks.add(check);
        });
        adsSatIssuedCheckMapper.insertBatchSomeColumn(checks);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateDistrictRevokeCzfp(updateDistrictRevokeCzfpReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        if (adsSatIssuedBatch.getReported()) {
            throw new BizException("该批次已上报，无法撤销分片！");
        }
        Set<String> nsrmcs = adsSatIssuedNsrxxMapper.listDistrictIssued(req.getParentId(), req.getQyids());
        if (nsrmcs.size() < 3) {
            nsrmcs.addAll(adsSatIssuedNsrxxMapper.listDistrictCheck(req.getParentId(), req.getQyids(), adsSatIssuedBatch.getAreaCode(), false));
        }
        MsgUtils.checkOperate(nsrmcs, "不可撤销分片！");
        QueryWrapper<AdsSatIssuedCheck> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(AdsSatIssuedCheck::getQyid, req.getQyids())
                .eq(AdsSatIssuedCheck::getBatchNo, adsSatIssuedBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getXzqhDm, adsSatIssuedBatch.getAreaCode())
                .eq(AdsSatIssuedCheck::getCheckType, 3);
        adsSatIssuedCheckMapper.delete(wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateDistrictReport(IdStringReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        if (adsSatIssuedBatch.getReported()) {
            throw new BizException("该批次已提交到市级，无法确认上报！");
        }
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getParentId, req.getId())
                .eq(AdsSatIssuedBatch::getConfirmed, false);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("街道未完全确认，无法确认上报！");
        }
        adsSatIssuedBatch.setReported(true);
        adsSatIssuedBatchMapper.updateById(adsSatIssuedBatch);
        List<AdsSatIssuedCheck> checks = new ArrayList<>();
        String userCode = userSession.getUserCode();
        String areaCode = userSession.getDefaultAreaCode();
        LocalDateTime now = LocalDateTime.now();
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, adsSatIssuedBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getXzqhDm, areaCode)
                .in(AdsSatIssuedCheck::getCheckType, CollUtil.list(false, 3, 4));
        List<AdsSatIssuedCheck> list = adsSatIssuedCheckMapper.selectList(checkWrapper);
        list.forEach(check -> {
            AdsSatIssuedCheck newCheck = new AdsSatIssuedCheck();
            newCheck.setId(IdUtil.getSnowflakeNextIdStr());
            newCheck.setBatchNo(adsSatIssuedBatch.getBatchNo());
            newCheck.setQyid(check.getQyid());
            newCheck.setXzqhDm(check.getXzqhDm());
            newCheck.setJdxzDm(check.getJdxzDm());
            newCheck.setCheckType(5);
            newCheck.setCreateUser(userCode);
            newCheck.setCreateTime(now);
            checks.add(newCheck);
        });
        if (CollUtil.isNotEmpty(checks)) {
            adsSatIssuedCheckMapper.insertBatchSomeColumn(checks);
        }

    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateDistrictRevokeReport(IdStringReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        if (!adsSatIssuedBatch.getReported()) {
            throw new BizException("该批次未提交到市级，无法撤销上报！");
        }
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, adsSatIssuedBatch.getBatchNo())
                .eq(AdsSatIssuedBatch::getReported, false);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) <= 0) {
            throw new BizException("所有区级已经上报，无法撤销上报！");
        }
        adsSatIssuedBatch.setReported(false);
        adsSatIssuedBatchMapper.updateById(adsSatIssuedBatch);
        String areaCode = userSession.getDefaultAreaCode();
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, adsSatIssuedBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getCheckType, 5)
                .eq(AdsSatIssuedCheck::getXzqhDm, areaCode);
        adsSatIssuedCheckMapper.delete(checkWrapper);
    }

    @Override
    public Page<DistrictIssuedCheckVo> pageDistrictIssuedCheck(PageDistrictIssuedCheckReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        if (ObjectUtil.isNull(req.getCheckStatus())) {
            req.setCheckStatus(0);
        }
        if (ObjectUtil.isNull(req.getIssuedStatus())) {
            req.setIssuedStatus(0);
        }
        req.setBatchNo(adsSatIssuedBatch.getBatchNo());
        req.setAreaCode(userSession.getDefaultAreaCode());
        return adsSatIssuedNsrxxMapper.pageDistrictIssuedCheck(req.getPage(DistrictIssuedCheckVo.class), req);
    }

    @Override
    public List<SingleSelectVo> listRevokeIssuedStreet(BatchNoReq req) {
        return adsSatIssuedBatchMapper.listRevokeIssuedStreet(req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public StreetRevokeIssuedVo updateStreetRevokeIssued(UpdateStreetRevokeIssuedReq req) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .in(AdsSatIssuedBatch::getAreaCode, req.getJdxzDms())
                .eq(AdsSatIssuedBatch::getReported, true);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("存在已提交街道，无法撤销下发！");
        }
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, req.getBatchNo())
                .in(AdsSatIssuedCheck::getJdxzDm, req.getJdxzDms())
                .eq(AdsSatIssuedCheck::getCheckType, 1);
        if (adsSatIssuedCheckMapper.selectCount(checkWrapper) > 0) {
            throw new BizException("存在已操作街道，无法撤销下发！");
        }
        StreetRevokeIssuedVo vo = new StreetRevokeIssuedVo();
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .in(AdsSatIssuedBatch::getAreaCode, req.getJdxzDms());
        adsSatIssuedBatchMapper.delete(wrapper);
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo());
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            vo.setIsRedirect(false);
            vo.setMsg("已完成撤销下发操作！");
        } else {
            checkWrapper = new QueryWrapper<>();
            checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, req.getBatchNo());
            adsSatIssuedCheckMapper.delete(checkWrapper);
            vo.setIsRedirect(true);
            vo.setMsg("该批次已全部撤销，将返回上一页面，请重新操作！");
        }
        return vo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateStreetCzfp(UpdateStreetCzfpReq req) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getConfirmed, true);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("区级已确认，不可财政分片！");
        }
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getReported, false);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("街道未全部上报，不可财政分片！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次不存在，请联系管理员！");
        }
        String xzqhDm = userSession.getDefaultAreaCode();
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, parentBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getCheckType, 2)
                .eq(AdsSatIssuedCheck::getXzqhDm, xzqhDm)
                .in(AdsSatIssuedCheck::getQyid, req.getQyids());
        adsSatIssuedCheckMapper.delete(checkWrapper);
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        List<AdsSatIssuedCheck> checks = new ArrayList<>();
        req.getQyids().forEach(qyid -> {
            AdsSatIssuedCheck check = new AdsSatIssuedCheck();
            check.setId(IdUtil.getSnowflakeNextIdStr());
            check.setQyid(qyid);
            check.setCheckType(2);
            check.setBatchNo(parentBatch.getBatchNo());
            check.setXzqhDm(xzqhDm);
            check.setJdxzDm(req.getJdxzDm());
            check.setCreateUser(userCode);
            check.setCreateTime(now);
            checks.add(check);
        });
        adsSatIssuedCheckMapper.insertBatchSomeColumn(checks);
    }

    @Override
    public List<SingleSelectVo> listStreetRevokeJdxz(BatchNoReq req) {
        return adsSatIssuedBatchMapper.listStreetRevokeJdxz(req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateStreetRevoke(UpdateStreetRevokeReq req) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getConfirmed, true);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("区级已确认，不可退回！");
        }
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getReported, false)
                .in(AdsSatIssuedBatch::getAreaCode, req.getJdxzDms());
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("存在未上报街道，不可退回！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次不存在，请联系管理员！");
        }
        // 删除所有checkType=2的核定记录
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, parentBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getXzqhDm, userSession.getDefaultAreaCode())
                .eq(AdsSatIssuedCheck::getCheckType, 2);
        adsSatIssuedCheckMapper.delete(checkWrapper);
        // 添加所有提交的街道的核定记录，不包含要回退的（回退的要删除）
        List<AdsSatIssuedCheck> checks = adsSatIssuedCheckMapper.listCheck(req.getParentId(), req.getJdxzDms());
        if (CollUtil.isNotEmpty(checks)) {
            List<AdsSatIssuedCheck> addChecks = new ArrayList<>();
            checks.forEach(check -> {
                AdsSatIssuedCheck addCheck = new AdsSatIssuedCheck();
                addCheck.setId(IdUtil.getSnowflakeNextIdStr());
                addCheck.setBatchNo(parentBatch.getBatchNo());
                addCheck.setQyid(check.getQyid());
                addCheck.setXzqhDm(check.getXzqhDm());
                addCheck.setJdxzDm(check.getJdxzDm());
                addCheck.setCheckType(2);
                addCheck.setCreateUser(check.getCreateUser());
                addCheck.setCreateTime(check.getCreateTime());
                addChecks.add(addCheck);
            });
            adsSatIssuedCheckMapper.insertBatchSomeColumn(addChecks);
        }

        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getReported, true)
                .in(AdsSatIssuedBatch::getAreaCode, req.getJdxzDms());
        AdsSatIssuedBatch updateBatch = new AdsSatIssuedBatch();
        updateBatch.setReported(false);
        adsSatIssuedBatchMapper.update(updateBatch, wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateStreetBatchConfirm(UpdateStreetBatchConfirmReq req) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getConfirmed, true);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("区级已确认，不可批次确定！");
        }
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getReported, false);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("存在未上报街道，不可批次确定！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次不存在，请联系管理员！");
        }
        String areaCode = userSession.getDefaultAreaCode();
        List<String> nsrmcs = adsSatIssuedCheckMapper.listManyStreetCheckNsrmc(parentBatch.getBatchNo(), areaCode);
        MsgUtils.checkOperate(nsrmcs, "存在多街道核定，请完成财政分片后再试！");
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, parentBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getCheckType, 2)
                .eq(AdsSatIssuedCheck::getXzqhDm, userSession.getDefaultAreaCode());
        AdsSatIssuedCheck updateCheck = new AdsSatIssuedCheck();
        updateCheck.setCheckType(4);
        adsSatIssuedCheckMapper.update(updateCheck, checkWrapper);
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo());
        AdsSatIssuedBatch updateBatch = new AdsSatIssuedBatch();
        updateBatch.setConfirmed(true);
        adsSatIssuedBatchMapper.update(updateBatch, wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateStreetBatchReview(UpdateStreetBatchConfirmReq req) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo())
                .eq(AdsSatIssuedBatch::getConfirmed, false);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) > 0) {
            throw new BizException("区级未确认，不可批次重核！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次不存在，请联系管理员！");
        }
        if (parentBatch.getReported()) {
            throw new BizException("该父批次已提交，不可批次重核！");
        }
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, parentBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getCheckType, 4)
                .eq(AdsSatIssuedCheck::getXzqhDm, userSession.getDefaultAreaCode());
        AdsSatIssuedCheck updateCheck = new AdsSatIssuedCheck();
        updateCheck.setCheckType(2);
        adsSatIssuedCheckMapper.update(updateCheck, checkWrapper);
        wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo());
        AdsSatIssuedBatch updateBatch = new AdsSatIssuedBatch();
        updateBatch.setConfirmed(false);
        adsSatIssuedBatchMapper.update(updateBatch, wrapper);
    }

    @Override
    public Page<StreetIssuedCheckVo> pageStreetIssuedCheck(PageStreetIssuedCheckReq req) {
        if (ObjectUtil.isNull(req.getCheckType())) {
            req.setCheckType(0);
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次不存在，请联系管理员！");
        }
        req.setParentBatchNo(parentBatch.getBatchNo());
        req.setAreaCode(userSession.getDefaultAreaCode());
        return adsSatIssuedNsrxxMapper.pageStreetIssuedCheck(req.getPage(StreetIssuedCheckVo.class), req);
    }

    @Override
    public DistrictStatusVo getDistrictIssuedCheckStatus(IdStringReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getId());
        if (ObjectUtil.isNull(adsSatIssuedBatch)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        DistrictStatusVo vo = new DistrictStatusVo();
        vo.setParentStatus(adsSatIssuedBatch.getReported() ? 1 : 0);
        return vo;
    }

    @Override
    public DistrictStatusVo getStreetIssuedCheckStatus(GetStreetIssuedCheckStatusReq req) {
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, req.getBatchNo());
        List<AdsSatIssuedBatch> list = adsSatIssuedBatchMapper.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            throw new BizException("该批次不存在，请联系管理员！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(req.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次不存在，请联系管理员！");
        }
        DistrictStatusVo vo = new DistrictStatusVo();
        vo.setParentStatus(parentBatch.getReported() ? 1 : 0);
        if (parentBatch.getReported()) {
            vo.setChildStatus(3);
        } else {
            boolean reported = true;
            boolean confirmed = true;
            for (AdsSatIssuedBatch batch : list) {
                if (!batch.getConfirmed()) {
                    confirmed = false;
                    break;
                }
            }
            for (AdsSatIssuedBatch batch : list) {
                if (!batch.getReported()) {
                    reported = false;
                    break;
                }
            }
            if (confirmed) {
                vo.setChildStatus(3);
            } else {
                vo.setChildStatus(reported ? 2 : 1);
            }
        }
        return vo;
    }
}
