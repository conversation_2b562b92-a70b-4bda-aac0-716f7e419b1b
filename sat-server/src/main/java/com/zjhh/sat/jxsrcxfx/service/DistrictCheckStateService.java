package com.zjhh.sat.jxsrcxfx.service;


import com.zjhh.cz.req.IdsStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.PageDistrictCheckStateReq;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.UpdateCzfpReq;
import com.zjhh.sat.jxsrcxfx.vo.districtcheckstate.DistrictCheckStateVo;

/**
 * 区级核定情况
 *
 * <AUTHOR>
 * @since 2023/2/15 17:21
 */
public interface DistrictCheckStateService {

    /**
     * 列表
     *
     * @param req
     * @return
     */
    Page<DistrictCheckStateVo> pageDistrictCheckState(PageDistrictCheckStateReq req);

    /**
     * 财政分片
     *
     * @param req
     */
    void updateCzfp(UpdateCzfpReq req);

    /**
     * 确认
     *
     * @param req
     */
    void updateCheck(IdsStringReq req);

    /**
     * 作废
     *
     * @param req
     */
    void updateCancel(IdsStringReq req);
}
