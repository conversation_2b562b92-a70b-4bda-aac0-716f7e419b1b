package com.zjhh.sat.jxsrcxfx.service;

import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.cz.req.IdStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.*;
import com.zjhh.sat.jxsrcxfx.vo.districtentcheck.*;

import java.util.List;

/**
 * 区级企业核定
 *
 * <AUTHOR>
 * @since 2023/2/15 15:37
 */
public interface DistrictEntCheckService {

    /**
     * 区级企业核定-区级企业核定批次-列表
     *
     * @param req
     * @return
     */
    Page<DistrictIssuedBatchVo> pageDistrictIssuedBatch(PageDistrictEntIssuedBatchReq req);

    /**
     * 区级企业核定-企业核定下发表-区级下发给街道
     *
     * @param req
     */
    void updateDistrictIssued(UpdateDistrictIssuedReq req);

    /**
     * 区级企业核定-企业核定下发表-财政分片
     *
     * @param req
     */
    void updateDistrictCzfp(UpdateDistrictCzfpReq req);

    /**
     * 区级企业核定-企业核定下发表-撤销财政分片
     *
     * @param req
     */
    void updateDistrictRevokeCzfp(updateDistrictRevokeCzfpReq req);

    /**
     * 区级企业核定-企业核定下发表-确认上报
     *
     * @param req
     */
    void updateDistrictReport(IdStringReq req);

    /**
     * 区级企业核定-企业核定下发表-撤销上报
     *
     * @param req
     */
    void updateDistrictRevokeReport(IdStringReq req);

    /**
     * 区级企业核定-企业核定下发表-列表
     *
     * @param req
     * @return
     */
    Page<DistrictIssuedCheckVo> pageDistrictIssuedCheck(PageDistrictIssuedCheckReq req);

    /**
     * 区级企业核定-街道企业核定情况-撤销下发-街道列表
     *
     * @param req
     * @return
     */
    List<SingleSelectVo> listRevokeIssuedStreet(BatchNoReq req);

    /**
     * 区级企业核定-街道企业核定情况-撤销下发
     *
     * @param req
     * @return
     */
    StreetRevokeIssuedVo updateStreetRevokeIssued(UpdateStreetRevokeIssuedReq req);

    /**
     * 区级企业核定-街道企业核定情况-财政分片
     *
     * @param req
     */
    void updateStreetCzfp(UpdateStreetCzfpReq req);

    /**
     * 区级企业核定-街道企业核定情况-退回-街道乡镇列表
     *
     * @param req
     * @return
     */
    List<SingleSelectVo> listStreetRevokeJdxz(BatchNoReq req);

    /**
     * 区级企业核定-街道企业核定情况-退回
     *
     * @param req
     */
    void updateStreetRevoke(UpdateStreetRevokeReq req);

    /**
     * 区级企业核定-街道企业核定情况-批次确定
     *
     * @param req
     */
    void updateStreetBatchConfirm(UpdateStreetBatchConfirmReq req);

    /**
     * 区级企业核定-街道企业核定情况-批次重核
     *
     * @param req
     */
    void updateStreetBatchReview(UpdateStreetBatchConfirmReq req);

    /**
     * 区级企业核定-街道企业核定情况-列表
     *
     * @param req
     * @return
     */
    Page<StreetIssuedCheckVo> pageStreetIssuedCheck(PageStreetIssuedCheckReq req);

    /**
     * 获取企业核定下发表批次状态
     *
     * @param req
     * @return
     */
    DistrictStatusVo getDistrictIssuedCheckStatus(IdStringReq req);

    /**
     * 获取街道企业核定情况批次状态
     *
     * @param req
     * @return
     */
    DistrictStatusVo getStreetIssuedCheckStatus(GetStreetIssuedCheckStatusReq req);
}
