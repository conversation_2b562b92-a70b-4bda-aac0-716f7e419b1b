package com.zjhh.sat.jxsrcxfx.service;

import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.PageEntCheckedIssuedReq;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.UpdateCzfqReq;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.UpdateIssuedReq;
import com.zjhh.sat.jxsrcxfx.request.entcheckedissued.UpdateWljzReq;
import com.zjhh.sat.jxsrcxfx.vo.entcheckedissued.EntCheckedIssuedVo;

/**
 * 企业核定下发
 *
 * <AUTHOR>
 * @since 2022/11/23 10:40
 */
public interface EntCheckedIssuedService {

    /**
     * 获取企业核定下发列表
     *
     * @param req
     * @return
     */
    Page<EntCheckedIssuedVo> pageEntCheckedIssued(PageEntCheckedIssuedReq req);

    /**
     * 修改为外来建筑
     *
     * @param req
     */
    void updateWljz(UpdateWljzReq req);

    /**
     * 修改财政分片
     *
     * @param req
     */
    void updateCzfp(UpdateCzfqReq req);

    /**
     * 市级下发给区
     *
     * @param req
     */
    void updateIssued(UpdateIssuedReq req);

}
