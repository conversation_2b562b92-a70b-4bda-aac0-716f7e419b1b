package com.zjhh.sat.jxsrcxfx.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedNsrxx;
import com.zjhh.sat.jxsrcxfx.dto.CheckNsrxxDto;
import com.zjhh.sat.jxsrcxfx.request.districtcheckstate.PageDistrictCheckStateReq;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.PageDistrictIssuedCheckReq;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.PageStreetIssuedCheckReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.PageStreetCheckIssuedReq;
import com.zjhh.sat.jxsrcxfx.vo.districtcheckstate.DistrictCheckStateVo;
import com.zjhh.sat.jxsrcxfx.vo.districtentcheck.DistrictIssuedCheckVo;
import com.zjhh.sat.jxsrcxfx.vo.districtentcheck.StreetIssuedCheckVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetCheckIssuedVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 下发对应的企业信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface AdsSatIssuedNsrxxMapper extends CustomerBaseMapper<AdsSatIssuedNsrxx> {

    /**
     * 判断区级父批次下子批次是否存在已下发
     *
     * @param parentId
     * @param qyids
     * @return
     */
    Set<String> listDistrictIssued(@Param("parentId") String parentId, @Param("qyids") List<String> qyids);

    /**
     * 判断区级父批次下子批次是否存在核定
     *
     * @param parentId
     * @param qyids
     * @return
     */
    Set<String> listDistrictCheck(@Param("parentId") String parentId, @Param("qyids") List<String> qyids, @Param("areaCode") String areaCode, @Param("checked") Boolean checked);

    /**
     * 企业核定下发表-列表
     *
     * @param req
     * @return
     */
    Page<DistrictIssuedCheckVo> pageDistrictIssuedCheck(Page<DistrictIssuedCheckVo> page, @Param("req") PageDistrictIssuedCheckReq req);

    /**
     * 区级企业核定-街道企业核定情况-列表
     *
     * @param req
     * @return
     */
    Page<StreetIssuedCheckVo> pageStreetIssuedCheck(Page<StreetIssuedCheckVo> page, @Param("req") PageStreetIssuedCheckReq req);

    /**
     * 区级核定情况-列表
     *
     * @param req
     * @return
     */
    Page<DistrictCheckStateVo> pageDistrictCheckState(Page<DistrictCheckStateVo> page, @Param("req") PageDistrictCheckStateReq req);

    /**
     * 获取多个核定结果的企业信息
     *
     * @param adsSatIssuedNsrxxIds
     * @return
     */
    List<String> listManyCheck(@Param("ids") List<String> adsSatIssuedNsrxxIds);

    /**
     * 获取企业确定核定结果
     *
     * @param adsSatIssuedNsrxxIds
     * @return
     */
    List<CheckNsrxxDto> listCheckNsrxx(@Param("ids") List<String> adsSatIssuedNsrxxIds);

    /**
     * 企业核定下发表-列表
     *
     * @param req
     * @return
     */
    Page<StreetCheckIssuedVo> pageStreetCheckIssued(Page<StreetCheckIssuedVo> page, @Param("req") PageStreetCheckIssuedReq req);

}
