package com.zjhh.sat.jxsrcxfx.request.streetentcheck;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2023/2/16 14:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageStreetEntCheckReq extends PageReq {

    @Serial
    private static final long serialVersionUID = -8495392523020582461L;

    @Schema(description = "提交状态: 0-未提交到区级 1-已提交到区级")
    @NotNull(message = "提交状态不能为空！")
    @Min(0)
    @Max(1)
    private Integer issuedStatus;

    @Schema(hidden = true)
    private String areaCode;

    @Schema(hidden = true)
    private Boolean reported;
}
