package com.zjhh.sat.jxsrcxfx.dao.mapper;

import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedBatch;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.BatchNoReq;
import com.zjhh.sat.jxsrcxfx.request.districtentcheck.PageDistrictEntIssuedBatchReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.ListBatchStreetEntReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.PageStreetEntCheckReq;
import com.zjhh.sat.jxsrcxfx.vo.districtentcheck.DistrictIssuedBatchVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.BatchStreetEntVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetEntCheckVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业核定-下发批次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface AdsSatIssuedBatchMapper extends CustomerBaseMapper<AdsSatIssuedBatch> {

    /**
     * 区级企业核定批次-列表
     *
     * @param req
     * @return
     */
    Page<DistrictIssuedBatchVo> pageDistrictIssuedBatch(Page<DistrictIssuedBatchVo> page, @Param("req") PageDistrictEntIssuedBatchReq req);

    /**
     * 街道企业核定情况-撤销下发-街道列表
     *
     * @param req
     * @return
     */
    List<SingleSelectVo> listRevokeIssuedStreet(@Param("req") BatchNoReq req);

    /**
     * 区级企业核定-街道企业核定情况-退回-街道乡镇列表
     *
     * @param req
     * @return
     */
    List<SingleSelectVo> listStreetRevokeJdxz(@Param("req") BatchNoReq req);

    /**
     * 街道企业核定批次-列表
     *
     * @param req
     * @return
     */
    Page<StreetEntCheckVo> pageStreetEntCheck(Page<StreetEntCheckVo> page, @Param("req") PageStreetEntCheckReq req);

    /**
     * 导出所有批次街道
     *
     * @param req
     * @return
     */
    List<BatchStreetEntVo> listBatchStreetEnt(@Param("req") ListBatchStreetEntReq req);

}
