package com.zjhh.sat.jxsrcxfx.request.districtentcheck;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2023/2/13 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageDistrictEntIssuedBatchReq extends PageReq {

    @Serial
    private static final long serialVersionUID = 8576347043439935017L;

    @Schema(description = "提交状态：0-未提交到市级 1-已提交到市级")
    @NotNull(message = "提交状态不能为空！")
    private Integer commitType;

    @Schema(hidden = true)
    private Boolean reported;

    @Schema(hidden = true)
    private String areaCode;
}
