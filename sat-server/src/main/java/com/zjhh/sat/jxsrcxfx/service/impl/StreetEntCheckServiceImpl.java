package com.zjhh.sat.jxsrcxfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.cz.req.IdStringReq;
import com.zjhh.db.comm.Page;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedBatch;
import com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedCheck;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedBatchMapper;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedCheckMapper;
import com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedNsrxxMapper;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.ListBatchStreetEntReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.PageStreetCheckIssuedReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.PageStreetEntCheckReq;
import com.zjhh.sat.jxsrcxfx.request.streetentcheck.UpdateCzfpReq;
import com.zjhh.sat.jxsrcxfx.service.StreetEntCheckService;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.BatchStreetEntVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetCheckIssuedVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetEntCheckVo;
import com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetStatusVo;
import com.zjhh.user.service.impl.UserSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2023/2/16 14:08
 */
@Slf4j
@Service
@DS("tzjs")
public class StreetEntCheckServiceImpl implements StreetEntCheckService {

    @Resource
    private AdsSatIssuedNsrxxMapper adsSatIssuedNsrxxMapper;

    @Resource
    private AdsSatIssuedBatchMapper adsSatIssuedBatchMapper;

    @Resource
    private AdsSatIssuedCheckMapper adsSatIssuedCheckMapper;

    @Resource
    private UserSession userSession;

    @Override
    public Page<StreetEntCheckVo> pageStreetEntCheck(PageStreetEntCheckReq req) {
        req.setAreaCode(userSession.getDefaultAreaCode());
        req.setReported(req.getIssuedStatus() == 1);
        return adsSatIssuedBatchMapper.pageStreetEntCheck(req.getPage(StreetEntCheckVo.class), req);
    }

    @Override
    public Page<StreetCheckIssuedVo> pageStreetCheckIssued(PageStreetCheckIssuedReq req) {
        AdsSatIssuedBatch batch = adsSatIssuedBatchMapper.selectById(req.getBatchId());
        if (ObjectUtil.isNull(batch)) {
            throw new BizException("该批次号不存在，请联系管理员！");
        }
        req.setBatchNo(batch.getBatchNo());
        req.setAreaCode(userSession.getDefaultAreaCode());
        return adsSatIssuedNsrxxMapper.pageStreetCheckIssued(req.getPage(StreetCheckIssuedVo.class), req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateCzfp(UpdateCzfpReq req) {
        AdsSatIssuedBatch batch = adsSatIssuedBatchMapper.selectById(req.getBatchId());
        if (ObjectUtil.isNull(batch)) {
            throw new BizException("该批次号不存在，请联系管理员！");
        }
        if (batch.getReported()) {
            throw new BizException("该批次已经上报，无法财政分片！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(batch.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次号不存在，请联系管理员！");
        }
        QueryWrapper<AdsSatIssuedCheck> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, batch.getBatchNo())
                .in(AdsSatIssuedCheck::getQyid, req.getQyids())
                .eq(AdsSatIssuedCheck::getJdxzDm, batch.getAreaCode())
                .eq(AdsSatIssuedCheck::getCheckType, 1);
        if (adsSatIssuedCheckMapper.selectCount(wrapper) > 0) {
            throw new BizException("选中企业存在已核定信息，请撤销后再进行操作！");
        }
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        List<AdsSatIssuedCheck> checks = new ArrayList<>();
        req.getQyids().forEach(qyid -> {
            AdsSatIssuedCheck check = new AdsSatIssuedCheck();
            check.setId(IdUtil.getSnowflakeNextIdStr());
            check.setQyid(qyid);
            check.setBatchNo(batch.getBatchNo());
            check.setCheckType(1);
            check.setXzqhDm(parentBatch.getAreaCode());
            check.setJdxzDm(batch.getAreaCode());
            check.setCreateUser(userCode);
            check.setCreateTime(now);
            checks.add(check);
        });
        adsSatIssuedCheckMapper.insertBatchSomeColumn(checks);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateRevokeCzfp(UpdateCzfpReq req) {
        AdsSatIssuedBatch batch = adsSatIssuedBatchMapper.selectById(req.getBatchId());
        if (ObjectUtil.isNull(batch)) {
            throw new BizException("该批次号不存在，请联系管理员！");
        }
        if (batch.getReported()) {
            throw new BizException("该批次已经上报，无法财政分片！");
        }
        QueryWrapper<AdsSatIssuedCheck> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, batch.getBatchNo())
                .in(AdsSatIssuedCheck::getQyid, req.getQyids())
                .eq(AdsSatIssuedCheck::getJdxzDm, batch.getAreaCode())
                .eq(AdsSatIssuedCheck::getCheckType, 1);
        if (adsSatIssuedCheckMapper.selectCount(wrapper) != req.getQyids().size()) {
            throw new BizException("选中企业存在未核定信息！");
        }
        adsSatIssuedCheckMapper.delete(wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void UpdateReported(IdStringReq req) {
        AdsSatIssuedBatch batch = adsSatIssuedBatchMapper.selectById(req.getId());
        if (ObjectUtil.isNull(batch)) {
            throw new BizException("该批次号不存在，请联系管理员！");
        }
        if (batch.getReported()) {
            throw new BizException("该批次已经上报，无法确认上报！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(batch.getParentId());
        if (ObjectUtil.isNull(parentBatch)) {
            throw new BizException("该父批次号不存在，请联系管理员！");
        }
        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        QueryWrapper<AdsSatIssuedCheck> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, batch.getBatchNo())
                .eq(AdsSatIssuedCheck::getJdxzDm, batch.getAreaCode());
        List<AdsSatIssuedCheck> list = adsSatIssuedCheckMapper.selectList(wrapper);
        List<AdsSatIssuedCheck> checks = new ArrayList<>();
        list.forEach(issuedCheck -> {
            AdsSatIssuedCheck check = new AdsSatIssuedCheck();
            check.setId(IdUtil.getSnowflakeNextIdStr());
            check.setCheckType(2);
            check.setQyid(issuedCheck.getQyid());
            check.setBatchNo(parentBatch.getBatchNo());
            check.setJdxzDm(issuedCheck.getJdxzDm());
            check.setXzqhDm(issuedCheck.getXzqhDm());
            check.setCreateUser(userCode);
            check.setCreateTime(now);
            checks.add(check);
        });
        if (CollUtil.isNotEmpty(checks)) {
            adsSatIssuedCheckMapper.insertBatchSomeColumn(checks);
        }
        batch.setReported(true);
        adsSatIssuedBatchMapper.updateById(batch);
    }

    @Override
    public void updateRevokeReported(IdStringReq req) {
        AdsSatIssuedBatch batch = adsSatIssuedBatchMapper.selectById(req.getId());
        if (ObjectUtil.isNull(batch)) {
            throw new BizException("该批次号不存在，请联系管理员！");
        }
        if (!batch.getReported()) {
            throw new BizException("该批次未上报，无法撤销上报！");
        }
        AdsSatIssuedBatch parentBatch = adsSatIssuedBatchMapper.selectById(batch.getParentId());
        QueryWrapper<AdsSatIssuedBatch> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsSatIssuedBatch::getBatchNo, batch.getBatchNo())
                .eq(AdsSatIssuedBatch::getReported, false);
        if (adsSatIssuedBatchMapper.selectCount(wrapper) <= 0) {
            throw new BizException("所有街道都已上报，无法撤销上报！");
        }
        QueryWrapper<AdsSatIssuedCheck> checkWrapper = new QueryWrapper<>();
        checkWrapper.lambda().eq(AdsSatIssuedCheck::getBatchNo, parentBatch.getBatchNo())
                .eq(AdsSatIssuedCheck::getCheckType, 2)
                .eq(AdsSatIssuedCheck::getJdxzDm, batch.getAreaCode());
        adsSatIssuedCheckMapper.delete(checkWrapper);
        batch.setReported(false);
        adsSatIssuedBatchMapper.updateById(batch);
    }

    @Override
    public List<BatchStreetEntVo> listBatchStreetEnt(ListBatchStreetEntReq req) {
        req.setAreaCode(userSession.getDefaultAreaCode());
        req.setReported(req.getIssuedStatus() == 1);
        List<BatchStreetEntVo> list = adsSatIssuedBatchMapper.listBatchStreetEnt(req);
        Map<String, AtomicInteger> map = new HashMap<>();
        list.forEach(vo -> {
            AtomicInteger xh = map.get(vo.getBatchNo());
            if (ObjectUtil.isNull(xh)) {
                xh = new AtomicInteger(1);
                map.put(vo.getBatchNo(), xh);
            }
            vo.setXh(xh.getAndIncrement());
        });
        return list;
    }

    @Override
    public StreetStatusVo getStreetStatus(IdStringReq req) {
        AdsSatIssuedBatch adsSatIssuedBatch = adsSatIssuedBatchMapper.selectById(req.getId());
        StreetStatusVo vo = new StreetStatusVo();
        vo.setStatus(adsSatIssuedBatch.getReported() ? 1 : 0);
        return vo;
    }
}
