<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedBatchMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , batch_no, parent_id, area_code, reported, create_user, create_time
    </sql>

    <resultMap id="DistrictEntIssuedBatchVo" type="com.zjhh.sat.jxsrcxfx.vo.districtentcheck.DistrictIssuedBatchVo">
        <result property="id" column="id"/>
        <result property="batchNo" column="batch_no"/>
        <result property="parentId" column="parent_id"/>
        <result property="commitType" column="commit_type"/>
        <result property="areaName" column="area_name"/>
        <result property="createTime" column="create_time"/>
        <result property="uncommittedStreet" column="uncommitted_street"/>
        <result property="committedStreet" column="committed_street"/>
        <result property="noConfirmedNum" column="no_confirmed_num"/>
        <result property="uncommittedStreetList" column="uncommitted_street_list"/>
        <result property="committedStreetList" column="committed_street_list"/>
        <collection property="children" column="{commitType=commit_type,parentId=id}"
                    ofType="com.zjhh.sat.jxsrcxfx.vo.districtentcheck.DistrictIssuedBatchVo" select="listBatchChildren"/>
    </resultMap>

    <select id="pageDistrictIssuedBatch" resultMap="DistrictEntIssuedBatchVo">
        select t1.id, t1.batch_no, t2.jdxz_mc as area_name, t1.parent_id, #{req.commitType} as commit_type, t1.create_time
        from ads_sat_issued_batch t1 left join dm_tzjs_public_czfp t2
        on t1.area_code = t2.jdxz_dm
        where t1.reported = #{req.reported}
        and t1.area_code = #{req.areaCode}
        and t1.parent_id = 'root'
        <if test="req.commitType == 1">
            order by t1.batch_no desc
        </if>
        <if test="req.commitType == 0">
            order by t1.batch_no
        </if>
    </select>

    <select id="listBatchChildren" resultType="com.zjhh.sat.jxsrcxfx.vo.districtentcheck.DistrictIssuedBatchVo">
        select t1.batch_no,
        t1.parent_id,
        max(t1.create_time) as create_time,
        (select count(*)
        from ads_sat_issued_batch t
        where t.batch_no = t1.batch_no and t.reported = false) as uncommitted_street,
        (select count(*)
        from ads_sat_issued_batch t
        where t.batch_no = t1.batch_no and t.reported = true) as committed_street,
        (select count(*)
        from ads_sat_issued_batch t
        where t.batch_no = t1.batch_no and t.confirmed = false) as no_confirmed_num,
        (select string_agg(tt.jdxz_mc,',')
        from ads_sat_issued_batch t
        left join dm_tzjs_public_czfp tt on t.area_code = tt.jdxz_dm
        where t.batch_no = t1.batch_no
        and t.reported = false) as uncommitted_street_list,
        (select string_agg(tt.jdxz_mc,',')
        from ads_sat_issued_batch t
        left join dm_tzjs_public_czfp tt on t.area_code = tt.jdxz_dm
        where t.batch_no = t1.batch_no
        and t.reported = true) as committed_street_list,
        #{commitType} as commit_type
        from ads_sat_issued_batch t1
        where t1.parent_id = #{parentId}
        group by t1.batch_no, t1.parent_id
        <if test="commitType == 1">
            order by t1.batch_no desc
        </if>
        <if test="commitType == 0">
            order by t1.batch_no
        </if>
    </select>

    <select id="listRevokeIssuedStreet" resultType="com.zjhh.comm.vo.SingleSelectVo">
        select t2.jdxz_dm as code, t2.jdxz_dm as value, t2.jdxz_mc as title
        from ads_sat_issued_batch t1
            left join dm_tzjs_public_czfp t2 on t1.area_code = t2.jdxz_dm
        where t1.batch_no = #{req.batchNo}
    </select>

    <select id="listStreetRevokeJdxz" resultType="com.zjhh.comm.vo.SingleSelectVo">
        select t2.jdxz_dm as code, t2.jdxz_mc as title, t2.jdxz_dm as value
        from ads_sat_issued_batch t1
            left join dm_tzjs_public_czfp t2 on t1.area_code = t2.jdxz_dm
        where t1.batch_no = #{req.batchNo} and t1.reported = true and t1.confirmed = false
    </select>

    <select id="pageStreetEntCheck" resultType="com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetEntCheckVo">
        select t1.id, t1.batch_no, t2.jdxz_mc, t1.create_time
        from ads_sat_issued_batch t1
        left join dm_tzjs_public_czfp t2 on t1.area_code = t2.jdxz_dm
        where t1.parent_id != 'root'
        and t1.area_code = #{req.areaCode}
        and t1.reported = #{req.reported}
        <if test="req.issuedStatus == 0">
            order by t1.batch_no
        </if>
        <if test="req.issuedStatus == 1">
            order by t1.batch_no desc
        </if>
    </select>

    <select id="listBatchStreetEnt" resultType="com.zjhh.sat.jxsrcxfx.vo.streetentcheck.BatchStreetEntVo">
        select t1.batch_no,
        t5.jdxz_mc,
        t4.nsrsbh,
        t4.nsrmc,
        t4.hy_mc,
        t4.djzclx_mc,
        t4.scjydz,
        t4.zcdz,
        t4.swjg_mc,
        t4.shxydm
        from ads_sat_issued_batch t1
        left join ads_sat_issued_nsrxx t2 on t1.batch_no = t2.batch_no
        left join ads_sat_issued_check t3
        on t2.batch_no = t3.batch_no and t2.qyid = t3.qyid and t3.jdxz_dm = t1.area_code
        left join ads_sat_bddj_nsrxx t4 on t4.qyid = t2.qyid
        left join dm_tzjs_czfp t5 on t3.jdxz_dm = t5.jdxz_dm
        where t1.parent_id != 'root'
        and t1.area_code = #{req.areaCode}
        and t1.reported = #{req.reported}
        <if test="req.ids != null and req.ids.size > 0">
            and t1.id in
            <foreach collection="req.ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        order by t1.batch_no, t2.qyid
    </select>

</mapper>
