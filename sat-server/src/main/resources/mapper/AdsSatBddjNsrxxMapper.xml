<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatBddjNsrxxMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        qyid
        , nsrsbh, nsrmc, hy_dm, hy_mc, djzclx_dm, djzclx_mc, scjydz, zcdz, swjg_dm, swjg_mc, shxydm, yswljz
    </sql>

    <select id="listEntCheckedIssued" resultType="com.zjhh.sat.jxsrcxfx.vo.entcheckedissued.EntCheckedIssuedVo"
            fetchSize="10000">
        select t1.qyid, t3.xzqh_dm, t3.jdxz_dm, t3.xzqh_mc, t3.jdxz_mc, t2.nsrsbh, t2.nsrmc,
        t2.hy_mc4 as hy_mc, t2.djzclx_mc, t2.scjydz, t2.zcdz, t2.swjg_mc, t2.shxydm, count(1) over() as total
        from (select x.qyid, x.djxh, x.jdxz_dm, x.hd_flag, x.change_exp, x.data_owner, x.sjgs_dm, b.yswljz
        from ads_tzjs_public_nsrxx x
        inner join ads_sat_bddj_nsrxx b on x.qyid = b.qyid and x.djxh = b.djxh) t1
        inner join (select n.*, c.hy_dm1, c.hy_dm2, c.hy_dm3, c.hy_dm4, c.hy_mc1, c.hy_mc2,
        c.hy_mc3, c.hy_mc4, j.swjg_mc, d.djzclx_mc, z.nsrzt_mc
        from ads_sat_dj_public_nsrxx n
        left join dm_gy_hy_cj c on n.hy_dm = c.hy_dm
        left join dm_gy_swjg j on n.zgswj_dm = j.swjg_dm
        left join dm_dj_djzclx d on n.djzclx_dm = d.djzclx_dm
        left join dm_gy_nsrzt z on n.nsrzt_dm = z.nsrzt_dm) t2
        on t1.djxh = t2.djxh and t1.sjgs_dm = t2.xzqh_dm
        left join dm_tzjs_public_czfp t3 on t1.jdxz_dm = t3.jdxz_dm and t3.jdxz_dm not like '999%'
        where t1.data_owner = '3304'
        and coalesce(t2.djzclx_dm, '') not like '4%'
        and coalesce(t2.djzclx_dm, '') != '900'
        and coalesce(t2.fddbrxm, '') not in ('迁移车购税', '数据迁移')
        and coalesce(t2.kzztdjlx_dm, '') != '1140'
        and not exists(select 1 from ads_sat_wljz_nsrxx t where t.qyid = t1.qyid)
        <choose>
            <when test="req.checkStatus == 1">
                and (t1.jdxz_dm is null or t1.jdxz_dm = '999') and not exists (select t.qyid from ads_sat_issued_nsrxx t where t.qyid = t1.qyid and t.batch_type = 1 and t.status = 0)
            </when>
            <when test="req.checkStatus == 2">
                and t1.jdxz_dm is not null and t1.jdxz_dm != '999'
            </when>
            <when test="req.checkStatus == 3">
                and exists (select t.qyid from ads_sat_issued_nsrxx t where t.qyid = t1.qyid and t.batch_type = 1 and t.status = 0)
            </when>
            <when test="req.checkStatus == 4">
                and t1.yswljz = 'Y'
            </when>
            <otherwise>
                and 1 = 0
            </otherwise>
        </choose>
        <if test="req.searchKey != null and req.searchKey != ''">
            and (t2.nsrsbh like concat('%', #{req.searchKey}, '%')
            or t2.nsrmc like concat('%', #{req.searchKey}, '%')
            or t2.scjydz like concat('%', #{req.searchKey}, '%')
            or t2.hy_mc4 like concat('%', #{req.searchKey}, '%')
            or t3.xzqh_mc like concat('%', #{req.searchKey}, '%')
            or t3.jdxz_mc like concat('%', #{req.searchKey}, '%'))
        </if>
        order by ${req.sortType}
        <if test="req.limit > 0">
            limit #{req.limit} offset #{req.offset}
        </if>
    </select>

</mapper>
