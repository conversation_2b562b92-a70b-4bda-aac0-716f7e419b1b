<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedNsrxxMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , batch_no, batch_type, qyid, status, create_user, create_time
    </sql>

    <select id="listDistrictIssued" resultType="java.lang.String">
        select t2.nsrmc
        from ads_sat_issued_nsrxx t1
        left join v_ads_tzjs_public_nsrxx t2 on t1.qyid = t2.qyid and t2.data_owner='3304'
        where t1.batch_type = 2
        and t1.batch_no in (select batch_no from ads_sat_issued_batch t where t.parent_id = #{parentId})
        and t1.qyid in
        <foreach collection="qyids" item="qyid" open="(" close=")" separator=",">
            #{qyid}
        </foreach>
    </select>

    <select id="listDistrictCheck" resultType="java.lang.String">
        select t2.nsrmc
        from ads_sat_issued_nsrxx t1
        left join v_ads_tzjs_public_nsrxx t2 on t1.qyid = t2.qyid and t2.data_owner='3304'
        left join ads_sat_issued_check t3
        on t1.qyid = t3.qyid and t1.batch_no = t3.batch_no and t3.check_type = 3 and t3.xzqh_dm = #{areaCode}
        where t1.batch_no in (select batch_no from ads_sat_issued_batch t where t.id = #{parentId})
        and t1.qyid in
        <foreach collection="qyids" item="qyid" open="(" close=")" separator=",">#{qyid}</foreach>
        <if test="checked">
            and t3.qyid is not null
        </if>
        <if test="!checked">
            and t3.qyid is null
        </if>
    </select>

    <select id="pageDistrictIssuedCheck"
            resultType="com.zjhh.sat.jxsrcxfx.vo.districtentcheck.DistrictIssuedCheckVo" fetchSize="1000">
        select t1.qyid,
        t2.nsrsbh,
        t2.nsrmc,
        t2.hy_mc4 as hy_mc,
        t2.djzclx_mc,
        t2.scjydz,
        t2.zcdz,
        t2.swjg_mc,
        t2.shxydm,
        exists(select t.qyid
        from ads_sat_issued_nsrxx t
        where t.batch_no in (select tmp.batch_no from ads_sat_issued_batch tmp where tmp.parent_id = #{req.id})
        and t.qyid = t1.qyid
        and t.batch_type = 2) as issued,
        t4.jdxz_mc
        from v_ads_tzjs_public_nsrxx t2
        left join ads_sat_issued_nsrxx t1 on t1.qyid = t2.qyid
        left join ads_sat_issued_check t3
        on t3.batch_no = t1.batch_no and t3.qyid = t1.qyid and t3.check_type in (3, 4) and t3.xzqh_dm = #{req.areaCode}
        left join dm_tzjs_public_czfp t4 on t4.jdxz_dm = t3.jdxz_dm
        where t2.data_owner = '3304'
        and t1.batch_no = #{req.batchNo}
        <if test="req.searchKey != null and req.searchKey != ''">
            and (t2.nsrsbh like concat('%', #{req.searchKey}, '%')
            or t2.nsrmc like concat('%', #{req.searchKey}, '%')
            or t2.scjydz like concat('%', #{req.searchKey}, '%')
            or t2.hy_mc like concat('%', #{req.searchKey}, '%')
            or t4.jdxz_mc like concat('%', #{req.searchKey}, '%'))
        </if>
        <if test="req.checkStatus == 1">
            and t3.qyid is null
        </if>
        <if test="req.checkStatus == 2">
            and t3.qyid is not null
        </if>
        <if test="req.issuedStatus == 1">
            and not exists (select t.qyid from ads_sat_issued_nsrxx t where t.batch_no in (select tmp.batch_no from ads_sat_issued_batch tmp where tmp.parent_id = #{req.id})
            and t.qyid = t1.qyid
            and t.batch_type = 2 )
        </if>
        <if test="req.issuedStatus == 2">
            and exists (select t.qyid from ads_sat_issued_nsrxx t where t.batch_no in (select tmp.batch_no from ads_sat_issued_batch tmp where tmp.parent_id = #{req.id})
            and t.qyid = t1.qyid
            and t.batch_type = 2 )
        </if>
        order by t1.qyid
    </select>

    <select id="pageStreetIssuedCheck" resultType="com.zjhh.sat.jxsrcxfx.vo.districtentcheck.StreetIssuedCheckVo">
        select t2.qyid,
        t2.nsrsbh,
        t2.nsrmc,
        t2.hy_mc4 as hy_mc,
        t2.djzclx_mc,
        t2.scjydz,
        t2.zcdz,
        t2.swjg_mc,
        t2.shxydm,
        (select string_agg(tt.jdxz_mc, ',')
        from ads_sat_issued_check t
        left join dm_tzjs_public_czfp tt on t.jdxz_dm = tt.jdxz_dm
        where t.batch_no = #{req.parentBatchNo}
        and t.qyid = t1.qyid
        and t.check_type in (2, 4)
        and t.xzqh_dm = #{req.areaCode}
        ) as jdxzMc
        FROM v_ads_tzjs_public_nsrxx t2
        LEFT JOIN ads_sat_issued_nsrxx t1 ON t1.qyid = t2.qyid
        where t2.data_owner = '3304'
        and t1.batch_no = #{req.batchNo}
        <if test="req.checkType == 1">
            and (select count(1) from ads_sat_issued_check t
            where t.batch_no = #{req.parentBatchNo}
            and t.qyid = t1.qyid
            and t.check_type in (2, 4)
            and t.xzqh_dm = #{req.areaCode}
            ) = 0
        </if>
        <if test="req.checkType == 2">
            and (select count(1) from ads_sat_issued_check t
            where t.batch_no = #{req.parentBatchNo}
            and t.qyid = t1.qyid
            and t.check_type in (2, 4)
            and t.xzqh_dm = #{req.areaCode}
            ) = 1
        </if>
        <if test="req.checkType == 3">
            and (select count(1) from ads_sat_issued_check t
            where t.batch_no = #{req.parentBatchNo}
            and t.qyid = t1.qyid
            and t.check_type in (2, 4)
            and t.xzqh_dm = #{req.areaCode}
            ) > 1
        </if>
        <if test="req.searchKey != null and req.searchKey != ''">
            and (t2.nsrsbh like concat('%', #{req.searchKey},'%') or t2.nsrmc like concat('%', #{req.searchKey},'%')
            or t2.scjydz like concat('%', #{req.searchKey},'%') or t2.hy_mc like concat('%', #{req.searchKey},'%'))
        </if>
    </select>

    <select id="pageDistrictCheckState"
            resultType="com.zjhh.sat.jxsrcxfx.vo.districtcheckstate.DistrictCheckStateVo">
        select t1.id,
        t2.qyid,
        t2.nsrsbh,
        t2.nsrmc,
        t2.hy_mc4 as hy_mc,
        t2.djzclx_mc,
        t2.scjydz,
        t2.swjg_mc,
        t2.shxydm,
        t2.zcdz,
        t1.batch_no,
        (select string_agg(tt.jdxz_mc, ',' order by t.xzqh_dm)
        from ads_sat_issued_check t left join dm_tzjs_public_czfp tt on t.xzqh_dm = tt.jdxz_dm
        where t.batch_no = t1.batch_no
        and t.check_type = 5
        and t.qyid = t1.qyid) as xzqh_mc,
        (select string_agg(tt.jdxz_mc, ',' order by t.xzqh_dm, t.jdxz_dm)
        from ads_sat_issued_check t left join dm_tzjs_public_czfp tt on t.jdxz_dm = tt.jdxz_dm
        where t.batch_no = t1.batch_no
        and t.check_type = 5
        and t.qyid = t1.qyid) as jdxz_mc
        from ads_sat_issued_nsrxx t1
        join v_ads_tzjs_public_nsrxx t2 on t1.qyid = t2.qyid and t2.data_owner='3304'
        where t1.batch_type = 1
        <if test="req.checkStatus == 1">
            and t1.status in (1, 2)
        </if>
        <if test="req.checkStatus == 2">
            and t1.status = 0
        </if>
        <if test="req.checkStatus == 3">
            and t1.status = 3
        </if>
        <if test="req.issuedStatus == 0">
            AND EXISTS (SELECT 1 FROM ads_sat_issued_batch t WHERE t.parent_id = 'root' AND t.reported = false AND
            t.batch_no = t1.batch_no)
        </if>
        <if test="req.issuedStatus == 1">
            AND EXISTS (SELECT 1 FROM ads_sat_issued_batch t WHERE t.parent_id = 'root' AND t.reported = true AND
            t.batch_no = t1.batch_no)
        </if>
        <if test="req.checkType == 1">
            and (select count(1) from ads_sat_issued_check t where t.batch_no = t1.batch_no and t.qyid = t1.qyid and
            check_type = 5) = 0
        </if>
        <if test="req.checkType == 2">
            and (select count(1) from ads_sat_issued_check t where t.batch_no = t1.batch_no and t.qyid = t1.qyid and
            check_type = 5) = 1
        </if>
        <if test="req.checkType == 3">
            and (select count(1) from ads_sat_issued_check t where t.batch_no = t1.batch_no and t.qyid = t1.qyid and
            check_type = 5) > 1
        </if>
        <if test="req.searchKey != null and req.searchKey != ''">
            and (t2.nsrsbh like concat('%', #{req.searchKey},'%') or t2.nsrmc like concat('%', #{req.searchKey},'%')
            or t2.scjydz like concat('%', #{req.searchKey},'%') or t2.hy_mc like concat('%', #{req.searchKey},'%'))
        </if>
        order by t1.qyid
    </select>

    <select id="listManyCheck" resultType="java.lang.String">
        select t2.nsrmc
        from ads_sat_issued_nsrxx t1
        left join v_ads_tzjs_public_nsrxx t2 on t1.qyid = t2.qyid and t2.data_owner='3304'
        where t1.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and (select count(1) from ads_sat_issued_check t where t.batch_no = t1.batch_no and t.qyid = t1.qyid and
        check_type = 5) > 1
    </select>

    <select id="listCheckNsrxx" resultType="com.zjhh.sat.jxsrcxfx.dto.CheckNsrxxDto">
        select t1.id, t1.qyid, t1.batch_no, t2.xzqh_dm, t2.jdxz_dm
        from ads_sat_issued_nsrxx t1
        left join ads_sat_issued_check t2 on t1.qyid = t2.qyid and t1.batch_no = t2.batch_no and t2.check_type = 5
        where t1.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="pageStreetCheckIssued" resultType="com.zjhh.sat.jxsrcxfx.vo.streetentcheck.StreetCheckIssuedVo">
        select t1.id,
        t1.qyid,
        t4.jdxz_mc,
        t3.nsrsbh,
        t3.nsrmc,
        t3.hy_mc4 as hy_mc,
        t3.djzclx_mc,
        t3.scjydz,
        t3.zcdz,
        t3.swjg_mc,
        t3.shxydm
        from v_ads_tzjs_public_nsrxx t3
        left join ads_sat_issued_nsrxx t1 on t1.qyid = t3.qyid
        left join ads_sat_issued_check t2
        on t1.batch_no = t2.batch_no and t1.qyid = t2.qyid and t2.jdxz_dm = #{req.areaCode} and t2.check_type = 1
        left join dm_tzjs_public_czfp t4 on t2.jdxz_dm = t4.jdxz_dm
        where t2.data_owner = '3304'
        and t1.batch_no = #{req.batchNo}
        <if test="req.checkStatus == 1">
            and t2.id is null
        </if>
        <if test="req.checkStatus == 2">
            and t2.id is not null
        </if>
        <if test="req.searchKey != null and req.searchKey != ''">
            and (t3.nsrsbh like concat('%', #{req.searchKey}, '%')
            or t3.nsrmc like concat('%', #{req.searchKey}, '%')
            or t3.scjydz like concat('%', #{req.searchKey}, '%')
            or t3.hy_mc like concat('%', #{req.searchKey}, '%'))
        </if>
        order by t1.qyid
    </select>

</mapper>
