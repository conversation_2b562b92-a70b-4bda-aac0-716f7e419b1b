<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.sat.jxsrcxfx.dao.mapper.AdsSatIssuedCheckMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , batch_no, qyid, xzqh_dm, jdxz_dm, create_user, create_time
    </sql>

    <select id="listManyStreetCheckNsrmc" resultType="java.lang.String">
        select t2.nsrmc
        from (select qyid
              from ads_sat_issued_check
              where batch_no = #{batchNo} and check_type = 2 and xzqh_dm = #{areaCode}
              group by qyid
              having count(qyid) > 1) t1
                 left join v_ads_tzjs_public_nsrxx t2 on t1.qyid = t2.qyid and t2.data_owner='3304'
    </select>

    <select id="listCheck" resultType="com.zjhh.sat.jxsrcxfx.dao.entity.AdsSatIssuedCheck">
        select t1.* from ads_sat_issued_check t1 left join
        ads_sat_issued_batch t2 on t1.batch_no = t2.batch_no and t1.jdxz_dm = t2.area_code
        where t1.check_type = 1 and t2.parent_id = #{parentId} and t2.reported = true
        and t1.jdxz_dm not in
        <foreach collection="jdxzDms" item="jdxzDm" separator="," open="(" close=")">
            #{jdxzDm}
        </foreach>
    </select>

    <delete id="deleteBatch">
        <foreach collection="list" item="item" open="" close="" separator=";">
            delete from ads_sat_issued_check
            where qyid = #{item.qyid} and batch_no = #{item.batchNo}
        </foreach>
    </delete>
</mapper>
